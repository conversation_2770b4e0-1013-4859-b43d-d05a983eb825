{% extends "share_base.html" %}

{% block title %}账号信息 - ID共享租用系统{% endblock %}

{% block content %}
<!-- 激活成功提示 -->
<div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6 mb-8 fade-in">
    <div class="flex items-start">
        <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <i class="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
        </div>
        <div class="ml-4">
            <h3 class="text-lg font-semibold text-green-800 mb-2">分享链接已激活！</h3>
            <p class="text-green-700 mb-2">您的分享链接已成功激活，有效期24小时。请妥善保管以下账号信息。</p>
            <p class="text-sm text-green-600">
                <i class="far fa-clock mr-1"></i>
                激活时间：{{ activated_at | local_time }} ({{ '' | timezone_name }})
            </p>
        </div>
    </div>
</div>

{% if accounts|length == 0 %}
    <!-- 无账号提示 -->
    <div class="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-8 text-center fade-in">
        <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-exclamation-triangle text-yellow-600 text-2xl"></i>
        </div>
        <h3 class="text-xl font-semibold text-yellow-800 mb-2">暂无可用账号</h3>
        <p class="text-yellow-700">当前账号池中没有可用的账号信息，请稍后再试或联系客服。</p>
    </div>
{% else %}
    <!-- 账号卡片网格 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {% for account in accounts %}
            <div class="bg-white rounded-xl shadow-lg card-hover border border-gray-100 overflow-hidden fade-in">
                <!-- 状态指示条 -->
                <div class="h-1 {% if account.status == 'normal' %}bg-green-500{% else %}bg-red-500{% endif %}"></div>
                
                <div class="p-6">
                    <!-- 账号标题 -->
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user text-blue-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800">账号信息</h3>
                                <p class="text-xs text-gray-500">Apple ID</p>
                            </div>
                        </div>
                        <!-- 状态徽章 -->
                        {% if account.status == 'normal' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <span class="status-dot bg-green-500"></span>
                                正常
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <span class="status-dot bg-red-500"></span>
                                异常
                            </span>
                        {% endif %}
                    </div>
                    
                    <!-- 用户名和区域 -->
                    <div class="mb-4">
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-mono font-semibold text-gray-900">{{ account.username }}</span>
                            {% if account.get('region_display') %}
                                <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-indigo-100 text-indigo-800">
                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                    {{ account.region_display }}
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- 额外信息 -->
                    {% if account.get('frontend_remark') %}
                        <div class="mb-3 p-3 bg-blue-50 rounded-lg">
                            <div class="flex items-start">
                                <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-2"></i>
                                <div>
                                    <p class="text-sm font-medium text-blue-800">备注</p>
                                    <p class="text-sm text-blue-700">{{ account.frontend_remark }}</p>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                    
                    {% if account.get('last_check') %}
                        <div class="mb-4 text-sm text-gray-600">
                            <i class="far fa-clock mr-2"></i>
                            最后检查：{{ account.last_check }}
                        </div>
                    {% endif %}
                    
                    <!-- 操作按钮 -->
                    <div class="space-y-3">
                        <button class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center copy-btn" 
                                data-clipboard-text="{{ account.username }}"
                                onclick="copySuccess('用户名')">
                            <i class="far fa-copy mr-2"></i>
                            复制用户名
                        </button>
                        
                        {% if account.get('password') %}
                            <button class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center copy-btn" 
                                    data-clipboard-text="{{ account.password }}"
                                    onclick="copySuccess('密码')">
                                <i class="fas fa-key mr-2"></i>
                                复制密码
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% endif %}

<!-- 使用须知 -->
<div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 mb-8 fade-in">
    <div class="flex items-center mb-4">
        <div class="w-10 h-10 bg-amber-100 rounded-full flex items-center justify-center mr-3">
            <i class="fas fa-shield-alt text-amber-600"></i>
        </div>
        <h3 class="text-lg font-semibold text-gray-800">使用须知</h3>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="flex items-start">
            <i class="fas fa-lock text-blue-500 mt-1 mr-3"></i>
            <span class="text-gray-700">请妥善保管账号信息，不要与他人分享</span>
        </div>
        <div class="flex items-start">
            <i class="far fa-clock text-green-500 mt-1 mr-3"></i>
            <span class="text-gray-700">分享链接在激活后24小时内有效</span>
        </div>
        <div class="flex items-start">
            <i class="fas fa-headset text-purple-500 mt-1 mr-3"></i>
            <span class="text-gray-700">如遇账号问题，请及时联系客服</span>
        </div>
        <div class="flex items-start">
            <i class="fas fa-ban text-red-500 mt-1 mr-3"></i>
            <span class="text-gray-700">禁止用于违法违规用途</span>
        </div>
    </div>
</div>

<!-- 倒计时显示 -->
<div class="text-center bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 fade-in">
    <div class="flex items-center justify-center mb-2">
        <i class="far fa-clock text-blue-600 text-lg mr-2"></i>
        <span class="text-blue-800 font-medium">链接有效期</span>
    </div>
    <p class="text-gray-600 mb-3">
        本分享链接将在24小时后自动失效 ({{ '' | timezone_name }}时间)
    </p>
    <div id="countdown" class="text-2xl font-bold text-blue-600 pulse-animation"></div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 初始化剪贴板功能
var clipboard = new ClipboardJS('.copy-btn');

function copySuccess(type) {
    Swal.fire({
        title: '复制成功！',
        text: `${type}已复制到剪贴板`,
        icon: 'success',
        timer: 1500,
        showConfirmButton: false,
        timerProgressBar: true,
        toast: true,
        position: 'top-end'
    });
}

// 处理复制失败
clipboard.on('error', function(e) {
    Swal.fire({
        title: '复制失败',
        text: '请手动选择并复制',
        icon: 'error',
        toast: true,
        position: 'top-end',
        timer: 2000
    });
});

// 警告弹窗，所有设备都显示
function showAppleWarning() {
    let countdown = 10;
    Swal.fire({
        title: '<i class="fas fa-exclamation-triangle" style="color: #f59e0b;"></i> 重要提示',
        html: `
            <div class="text-left">
                <p class="mb-4 text-lg font-semibold">苹果设备用户请注意：</p>
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                    <div class="flex items-start">
                        <i class="fas fa-times-circle text-red-500 mt-1 mr-3"></i>
                        <div>
                            <h4 class="font-semibold text-red-800 mb-1">禁止操作：</h4>
                            <p class="text-red-700">请勿在 <strong>iPhone设置 → iCloud</strong> 中登录此账号</p>
                        </div>
                    </div>
                </div>
                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                    <div class="flex items-start">
                        <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                        <div>
                            <h4 class="font-semibold text-green-800 mb-1">正确操作：</h4>
                            <p class="text-green-700">仅在 <strong>App Store</strong> 中登录此账号下载软件</p>
                        </div>
                    </div>
                </div>
                <div class="text-center bg-gray-50 rounded-lg p-4">
                    <p class="text-gray-600 mb-2">违规操作可能导致锁机</p>
                    <div id="warningCountdown" class="text-blue-600 font-bold text-lg">
                        ${countdown} 秒后可关闭此提示
                    </div>
                </div>
            </div>
        `,
        icon: false,
        width: '500px',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        customClass: {
            popup: 'rounded-xl'
        },
        didOpen: () => {
            const countdownElement = document.getElementById('warningCountdown');
            const timer = setInterval(() => {
                countdown--;
                countdownElement.textContent = `${countdown} 秒后可关闭此提示`;
                if (countdown <= 0) {
                    clearInterval(timer);
                    Swal.update({
                        showConfirmButton: true,
                        confirmButtonText: '<i class="fas fa-check mr-2"></i> 我已知晓',
                        confirmButtonColor: '#3b82f6'
                    });
                    countdownElement.textContent = '现在可以关闭此提示';
                    countdownElement.className = 'text-green-600 font-bold text-lg';
                }
            }, 1000);
        }
    }).then((result) => {
        // 警告弹窗关闭后，显示登录教程
        if (result.isConfirmed) {
            showLoginTutorial();
        }
    });
}

// 显示登录教程GIF
function showLoginTutorial() {
    let tutorialCountdown = 10;
    Swal.fire({
        title: '登录教程',
        html: `
            <div class="text-center">
                <p class="mb-4 text-lg font-semibold">正确的App Store登录方法：</p>
                <div class="mb-4">
                    <img src="/static/login.gif" 
                         alt="App Store登录教程" 
                         style="max-width: 100%; height: auto; border-radius: 12px; box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div style="display: none; padding: 24px; background: #f8fafc; border-radius: 12px; margin-top: 16px; border: 2px dashed #cbd5e1;">
                        <i class="fas fa-image text-gray-400" style="font-size: 3rem;"></i>
                        <p class="text-gray-500 mt-3 mb-0 font-medium">教程图片加载中...</p>
                    </div>
                </div>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <div class="flex items-center justify-center">
                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                        <span class="text-blue-800 font-medium">请仔细观看教程，按照步骤操作以避免账号异常</span>
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-4">
                    <div id="tutorialCountdown" class="text-blue-600 font-bold text-lg">
                        ${tutorialCountdown} 秒后可关闭教程
                    </div>
                </div>
            </div>
        `,
        icon: false,
        width: '600px',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        customClass: {
            popup: 'tutorial-popup rounded-xl'
        },
        didOpen: () => {
            const countdownElement = document.getElementById('tutorialCountdown');
            const timer = setInterval(() => {
                tutorialCountdown--;
                countdownElement.textContent = `${tutorialCountdown} 秒后可关闭教程`;
                if (tutorialCountdown <= 0) {
                    clearInterval(timer);
                    Swal.update({
                        showConfirmButton: true,
                        confirmButtonText: '<i class="fas fa-check-circle mr-2"></i> 我已学会',
                        confirmButtonColor: '#10b981'
                    });
                    countdownElement.textContent = '现在可以关闭教程';
                    countdownElement.className = 'text-green-600 font-bold text-lg';
                }
            }, 1000);
        }
    });
}

// 页面加载后直接弹窗
document.addEventListener('DOMContentLoaded', function() {
    showAppleWarning();
    
    const activatedAt = new Date('{{ activated_at }}');
    const expiresAt = new Date(activatedAt.getTime() + 24 * 60 * 60 * 1000);
    
    function updateCountdown() {
        const now = new Date();
        const timeLeft = expiresAt - now;
        
        if (timeLeft <= 0) {
            // 页面已过期
            document.body.innerHTML = `
                <div class="min-h-screen bg-gray-50 flex items-center justify-center px-4">
                    <div class="max-w-md w-full bg-white rounded-xl shadow-lg p-8 text-center">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-clock text-red-600 text-2xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">分享链接已过期</h2>
                        <p class="text-gray-600 mb-6">该分享链接已过期，请重新购买新的分享链接。</p>
                        <a href="/" class="inline-flex items-center justify-center w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200">
                            <i class="fas fa-home mr-2"></i>
                            返回首页
                        </a>
                    </div>
                </div>
            `;
            return;
        }
        
        const hours = Math.floor(timeLeft / (1000 * 60 * 60));
        const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
        
        // 更新倒计时显示
        const countdownElement = document.getElementById('countdown');
        if (countdownElement) {
            countdownElement.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }
    
    // 每秒更新一次倒计时
    setInterval(updateCountdown, 1000);
    updateCountdown(); // 立即执行一次
});
</script>
{% endblock %} 