# 生产环境配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# Flask环境
FLASK_ENV=production
FLASK_HOST=127.0.0.1
FLASK_PORT=5000

# 安全配置
SECRET_KEY=your-very-secure-secret-key-here-change-this
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-admin-password
ADMIN_SESSION_TIMEOUT=3600
REQUIRE_ADMIN_AUTH=True
ADMIN_PATH=admin_panel_secure

# HTTPS和代理配置
USE_HTTPS=True
BEHIND_PROXY=True
TRUSTED_PROXIES=127.0.0.1,::1
BASE_URL=https://your-domain.com

# MySQL数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=idshare_user
MYSQL_PASSWORD=your-mysql-password
MYSQL_DATABASE=idshare_db
MYSQL_CHARSET=utf8mb4
MYSQL_COLLATION=utf8mb4_general_ci

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your-redis-password

# 系统配置
UPDATE_INTERVAL=5
SHARE_EXPIRE_HOURS=24
ACCOUNTS_PER_SHARE=4
TIMEZONE=Asia/Shanghai

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/var/log/idshare/app.log

# 安全配置
MAX_LINKS_PER_BATCH=10000
RATE_LIMIT_ENABLED=True

# 配置说明：
# 1. SECRET_KEY: 必须设置为强密码，用于session加密
# 2. ADMIN_PASSWORD: 管理员密码，不能使用默认值
# 3. BASE_URL: 设置为您的实际域名，包含https://
# 4. USE_HTTPS: 生产环境建议设置为True
# 5. BEHIND_PROXY: 使用nginx反向代理时设置为True
# 6. 数据库和Redis密码请设置为强密码
