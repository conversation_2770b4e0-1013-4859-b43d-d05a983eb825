import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """基础配置类"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-this'
    
    # MySQL配置
    MYSQL_HOST = os.environ.get('MYSQL_HOST') or 'localhost'
    MYSQL_PORT = int(os.environ.get('MYSQL_PORT', 3306))
    MYSQL_USER = os.environ.get('MYSQL_USER') or 'root'
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or ''
    MYSQL_DATABASE = os.environ.get('MYSQL_DATABASE') or 'idshare_db'
    MYSQL_CHARSET = os.environ.get('MYSQL_CHARSET') or 'utf8mb4'
    MYSQL_COLLATION = os.environ.get('MYSQL_COLLATION') or 'utf8mb4_general_ci'
    
    # Redis配置
    REDIS_HOST = os.environ.get('REDIS_HOST') or 'localhost'
    REDIS_PORT = int(os.environ.get('REDIS_PORT', 6379))
    REDIS_DB = int(os.environ.get('REDIS_DB', 0))
    REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD') or None
    # 处理空字符串的情况
    if REDIS_PASSWORD == '':
        REDIS_PASSWORD = None
    
    # 系统配置
    BASE_URL = os.environ.get('BASE_URL') or 'http://localhost:5000'
    UPDATE_INTERVAL = int(os.environ.get('UPDATE_INTERVAL', 5))
    SHARE_EXPIRE_HOURS = int(os.environ.get('SHARE_EXPIRE_HOURS', 24))
    ACCOUNTS_PER_SHARE = int(os.environ.get('ACCOUNTS_PER_SHARE', 4))  # 每个分享链接分配的账号数量
    TIMEZONE = os.environ.get('TIMEZONE') or 'Asia/Shanghai'  # 系统时区配置
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.environ.get('LOG_FILE') or 'app.log'
    
    # 安全配置
    MAX_LINKS_PER_BATCH = int(os.environ.get('MAX_LINKS_PER_BATCH', 10000))
    RATE_LIMIT_ENABLED = os.environ.get('RATE_LIMIT_ENABLED', 'False').lower() == 'true'
    
    # Admin鉴权配置
    ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME') or 'admin'
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD') or 'admin123'
    ADMIN_SESSION_TIMEOUT = int(os.environ.get('ADMIN_SESSION_TIMEOUT', 3600))
    REQUIRE_ADMIN_AUTH = os.environ.get('REQUIRE_ADMIN_AUTH', 'True').lower() == 'true'
    ADMIN_PATH = os.environ.get('ADMIN_PATH') or 'admin_panel_secure'  # 自定义管理员路径

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    FLASK_ENV = 'development'

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    FLASK_ENV = 'production'
    
    # 生产环境强制使用环境变量
    @classmethod
    def validate_production_config(cls):
        """验证生产环境必需的配置项"""
        required_vars = [
            'SECRET_KEY', 'MYSQL_HOST', 'MYSQL_USER', 
            'MYSQL_PASSWORD', 'MYSQL_DATABASE', 'BASE_URL'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.environ.get(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"生产环境缺少必需的环境变量: {', '.join(missing_vars)}")
        
        # 生产环境Admin密码检查
        if os.environ.get('ADMIN_PASSWORD') == 'admin123':
            raise ValueError("生产环境必须修改默认的ADMIN_PASSWORD")

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = True
    MYSQL_DATABASE = os.environ.get('TEST_DATABASE') or 'idshare_test_db'
    REDIS_DB = int(os.environ.get('TEST_REDIS_DB', 1))

# 配置映射
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config():
    """根据环境变量获取配置"""
    env = os.environ.get('FLASK_ENV', 'development')
    config_class = config.get(env, config['default'])
    
    # 生产环境验证
    if env == 'production':
        config_class.validate_production_config()
    
    return config_class 