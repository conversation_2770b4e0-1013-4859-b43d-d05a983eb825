# 管理员鉴权功能指南

ID共享租用系统现已支持管理员鉴权功能，确保只有授权用户才能访问系统管理功能。

## 功能特性

### 1. 鉴权保护
- **管理页面保护**：所有 `/admin` 路径需要登录验证
- **API接口保护**：管理相关的API接口需要admin权限
- **会话管理**：支持自动会话过期和安全退出

### 2. 灵活配置
- **可选鉴权**：可通过配置文件启用/禁用鉴权功能
- **会话时长**：可配置会话超时时间
- **密码安全**：生产环境强制修改默认密码

## 配置说明

### 1. 环境变量配置

在 `.env` 文件中配置以下项目：

```bash
# Admin 鉴权配置
ADMIN_USERNAME=admin                # 管理员用户名
ADMIN_PASSWORD=admin123             # 管理员密码（生产环境必须修改）
ADMIN_SESSION_TIMEOUT=3600          # 会话超时时间（秒）
REQUIRE_ADMIN_AUTH=True             # 是否启用管理员鉴权
```

### 2. 开发环境设置

```bash
# 开发环境可以使用默认配置
REQUIRE_ADMIN_AUTH=True
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
ADMIN_SESSION_TIMEOUT=3600
```

### 3. 生产环境设置

```bash
# 生产环境必须设置强密码
FLASK_ENV=production
REQUIRE_ADMIN_AUTH=True
ADMIN_USERNAME=your_admin_user
ADMIN_PASSWORD=your_secure_password_here
ADMIN_SESSION_TIMEOUT=1800          # 30分钟超时
```

**注意：** 生产环境如果使用默认密码 `admin123`，系统将拒绝启动！

## 使用方法

### 1. 启用鉴权功能

设置环境变量：
```bash
REQUIRE_ADMIN_AUTH=True
```

### 2. 禁用鉴权功能

```bash
REQUIRE_ADMIN_AUTH=False
```

当禁用时，所有管理功能无需登录即可访问。

### 3. 管理员登录

1. 访问首页 `http://localhost:5000`
2. 点击"管理面板"按钮
3. 系统自动跳转到登录页面
4. 输入管理员用户名和密码
5. 登录成功后进入管理界面

### 4. 会话管理

- **自动过期**：超过配置时间自动退出
- **手动退出**：点击管理界面右上角"退出登录"
- **保持登录**：登录时勾选"保持登录状态"可延长会话

## 安全特性

### 1. 会话保护
- 会话超时自动清除
- 服务器端会话验证
- 防止会话劫持

### 2. 访问控制
- 装饰器模式的权限验证
- 统一的重定向处理
- 友好的错误提示

### 3. 生产环境验证
- 强制密码复杂度检查
- 必需环境变量验证
- 安全配置提示

## 受保护的功能

### 管理页面
- `/admin` - 系统管理主页面
- `/admin/login` - 登录页面（无需鉴权）
- `/admin/logout` - 退出登录

### API接口
- `POST /api/add_share_url` - 添加账号源
- `POST /api/trigger_update` - 手动更新账号池
- `GET /api/stats` - 获取系统统计
- `GET /api/accounts` - 获取账号信息

### 无需鉴权的功能
- 首页访问
- 分享链接访问 `/share/{uuid}`
- 管理员登录页面

## 故障排除

### 1. 无法登录

**问题**：输入正确用户名密码仍无法登录
**检查**：
- 确认 `.env` 文件中的用户名密码配置
- 检查 `REQUIRE_ADMIN_AUTH` 是否为 `True`
- 查看应用日志是否有错误信息

### 2. 会话频繁过期

**问题**：登录后很快就要求重新登录
**解决**：
- 增加 `ADMIN_SESSION_TIMEOUT` 的值
- 登录时勾选"保持登录状态"

### 3. 生产环境启动失败

**问题**：
```
ValueError: 生产环境必须修改默认的ADMIN_PASSWORD
```
**解决**：
```bash
export ADMIN_PASSWORD=your_secure_password
```

### 4. 禁用鉴权后仍要求登录

**检查配置**：
```bash
echo $REQUIRE_ADMIN_AUTH
# 应该显示 False 或 false
```

**重新设置**：
```bash
export REQUIRE_ADMIN_AUTH=False
# 或在 .env 文件中设置
echo "REQUIRE_ADMIN_AUTH=False" >> .env
```

## 最佳实践

### 1. 密码安全
- 使用强密码（至少12位，包含大小写字母、数字、特殊字符）
- 定期更换管理员密码
- 不要在代码中硬编码密码

### 2. 会话管理
- 生产环境设置较短的会话超时时间（如30分钟）
- 及时退出登录，特别是在共享设备上
- 定期检查登录日志

### 3. 部署配置
- 开发环境可以使用默认配置
- 测试环境使用独立的管理员账号
- 生产环境使用强密码和短会话时间

### 4. 监控建议
- 监控管理员登录频率
- 记录重要管理操作日志
- 定期审查访问权限

## API调用示例

当启用鉴权后，API调用需要有效的管理员会话：

```javascript
// 正确的调用方式（在已登录的管理页面）
fetch('/api/stats')
  .then(response => {
    if (response.redirected) {
      // 会话过期，重定向到登录页
      window.location.href = response.url;
      return;
    }
    return response.json();
  })
  .then(data => console.log(data));

// 错误的调用方式（未登录状态）
// 将会收到重定向响应
```

## 配置参数说明

| 参数 | 默认值 | 说明 |
|-----|--------|------|
| `ADMIN_USERNAME` | admin | 管理员用户名 |
| `ADMIN_PASSWORD` | admin123 | 管理员密码 |
| `ADMIN_SESSION_TIMEOUT` | 3600 | 会话超时时间（秒） |
| `REQUIRE_ADMIN_AUTH` | True | 是否启用鉴权 |

通过合理配置这些参数，可以在安全性和便利性之间找到平衡。 