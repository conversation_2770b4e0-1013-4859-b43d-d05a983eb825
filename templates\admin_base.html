<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}管理员面板 - ID共享租用系统{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdmirror.com/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" 
          onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css';">
    
    <!-- Font Awesome 6.7.2 -->
    <link rel="stylesheet" href="https://cdn.jsdmirror.com/npm/@fortawesome/fontawesome-free@6.7.2/css/fontawesome.min.css"
          onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.7.2/css/fontawesome.min.css';">
    <link rel="stylesheet" href="https://cdn.jsdmirror.com/npm/@fortawesome/fontawesome-free@6.7.2/css/solid.min.css"
          onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.7.2/css/solid.min.css';">
    
    <style>
        /* Flat风格管理员界面样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
        }

        .admin-card {
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            transition: border-color 0.2s ease;
        }

        .admin-card:hover {
            border-color: #cbd5e1;
        }

        .sidebar {
            background: #ffffff;
            border-right: 1px solid #e2e8f0;
        }

        .sidebar-item {
            color: #64748b;
            transition: all 0.2s ease;
            border-radius: 6px;
            margin: 2px 0;
        }

        .sidebar-item:hover {
            background: #f1f5f9;
            color: #334155;
        }

        .sidebar-item.active {
            background: #3b82f6;
            color: #ffffff;
        }

        .sidebar-item i {
            width: 20px;
            text-align: center;
        }

        /* 简约按钮样式 */
        .btn-flat {
            background: #ffffff;
            border: 1px solid #d1d5db;
            color: #374151;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-flat:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-primary {
            background: #3b82f6;
            border: 1px solid #3b82f6;
            color: #ffffff;
        }

        .btn-primary:hover {
            background: #2563eb;
            border-color: #2563eb;
        }

        .btn-danger {
            background: #ef4444;
            border: 1px solid #ef4444;
            color: #ffffff;
        }

        .btn-danger:hover {
            background: #dc2626;
            border-color: #dc2626;
        }

        /* 顶部导航栏 */
        .top-nav {
            background: #ffffff;
            border-bottom: 1px solid #e2e8f0;
            height: 60px;
        }

        /* 页面标题 */
        .page-title {
            color: #1e293b;
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        /* 简约表格样式 */
        .table-flat {
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }

        .table-flat th {
            background: #f8fafc;
            color: #64748b;
            font-weight: 500;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 12px 16px;
            border-bottom: 1px solid #e2e8f0;
        }

        .table-flat td {
            padding: 12px 16px;
            border-bottom: 1px solid #f1f5f9;
            color: #334155;
        }

        .table-flat tr:hover {
            background: #f8fafc;
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- 统一的内容块 -->
    {% block content %}
    <!-- 默认管理页面布局 -->
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 sidebar transform -translate-x-full lg:translate-x-0 transition-transform duration-200 ease-in-out" id="sidebar">
        <!-- 侧边栏头部 -->
        <div class="flex items-center justify-center h-16 border-b border-gray-200">
            <h2 class="text-gray-800 text-lg font-semibold">
                <i class="fas fa-shield-alt mr-2 text-blue-600"></i>
                管理面板
            </h2>
        </div>

        <!-- 侧边栏菜单 -->
        <nav class="mt-6 px-3">
            <a href="/{{ config.ADMIN_PATH }}" class="sidebar-item group flex items-center px-3 py-3 text-sm font-medium mb-1">
                <i class="fas fa-chart-line mr-3"></i>
                仪表盘
            </a>

            <a href="/{{ config.ADMIN_PATH }}/shares" class="sidebar-item group flex items-center px-3 py-3 text-sm font-medium mb-1">
                <i class="fas fa-share-alt mr-3"></i>
                分享链接管理
            </a>

            <div class="border-t border-gray-200 mt-6 pt-6">
                <a href="/{{ config.ADMIN_PATH }}/logout" class="sidebar-item group flex items-center px-3 py-3 text-sm font-medium text-red-500 hover:text-red-600 hover:bg-red-50">
                    <i class="fas fa-sign-out-alt mr-3"></i>
                    退出登录
                </a>
            </div>
        </nav>
    </div>
    
    <!-- 主内容区域 -->
    <div class="lg:pl-64">
        <!-- 顶部导航栏 -->
        <div class="top-nav">
            <div class="px-6 lg:px-8">
                <div class="flex justify-between items-center h-full">
                    <div class="flex items-center">
                        <!-- 移动端菜单按钮 -->
                        <button type="button" class="lg:hidden p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100" onclick="toggleSidebar()">
                            <i class="fas fa-bars text-lg"></i>
                        </button>

                        <h1 class="page-title ml-4 lg:ml-0">{% block page_title %}管理面板{% endblock %}</h1>
                    </div>

                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-500 hidden sm:block">
                            <i class="fas fa-user-circle mr-1"></i>
                            管理员
                        </span>
                        <a href="/{{ config.ADMIN_PATH }}/logout" class="btn-flat text-red-500 hover:text-red-600 hover:bg-red-50 border-red-200">
                            <i class="fas fa-sign-out-alt mr-1"></i>
                            <span class="hidden sm:inline">退出</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <main class="py-8">
            <div class="max-w-7xl mx-auto px-6 lg:px-8">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="mb-6 p-4 rounded-lg border {% if category == 'success' %}bg-green-50 border-green-200 text-green-700{% elif category == 'danger' %}bg-red-50 border-red-200 text-red-700{% elif category == 'warning' %}bg-yellow-50 border-yellow-200 text-yellow-700{% else %}bg-blue-50 border-blue-200 text-blue-700{% endif %} fade-in">
                                <div class="flex items-center">
                                    <i class="fas {% if category == 'success' %}fa-check-circle{% elif category == 'danger' %}fa-exclamation-circle{% elif category == 'warning' %}fa-exclamation-triangle{% else %}fa-info-circle{% endif %} mr-2"></i>
                                    {{ message }}
                                </div>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block main_content %}{% endblock %}
            </div>
        </main>
    </div>
    {% endblock %}

    <!-- JavaScript库 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // 切换侧边栏显示/隐藏
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.toggle('-translate-x-full');
            }
        }
        
        // 高亮当前页面菜单项
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const menuItems = document.querySelectorAll('.sidebar-item');
            
            menuItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    item.classList.add('active');
                }
            });
        });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html> 