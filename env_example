# ID共享租用系统环境配置示例
# 复制此文件为 .env 并修改相应配置

# Flask 应用配置
FLASK_ENV=development
SECRET_KEY=EP0A9MVFQ9phyttrHvKyuEGZWckh
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
DEBUG=True

# MySQL 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=idshare
MYSQL_PASSWORD=bifT5esQP5ATXHT7
MYSQL_DATABASE=idshare
MYSQL_CHARSET=utf8mb4
MYSQL_COLLATION=utf8mb4_general_ci

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=8
REDIS_PASSWORD=
# 注意：如果Redis没有设置密码，请保持REDIS_PASSWORD为空或删除此行

# 系统配置
BASE_URL=http://0.0.0.0:5000
UPDATE_INTERVAL=5  # 账号池更新间隔（分钟）
SHARE_EXPIRE_HOURS=24  # 分享链接有效期（小时）
ACCOUNTS_PER_SHARE=4  # 每个分享链接分配的账号数量
TIMEZONE=Asia/Shanghai  # 系统时区配置

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=app.log

# 安全配置
MAX_LINKS_PER_BATCH=10000  # 单次批量生成最大链接数
RATE_LIMIT_ENABLED=False   # 是否启用访问限制

# Admin 鉴权配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
ADMIN_SESSION_TIMEOUT=3600  # 管理员会话超时时间（秒）
REQUIRE_ADMIN_AUTH=True     # 是否启用管理员鉴权
ADMIN_PATH=admin_panel_secure  # 自定义管理员路径，防止爆破 