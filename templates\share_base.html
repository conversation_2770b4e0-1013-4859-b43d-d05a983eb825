<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ID共享租用系统{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdmirror.com/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" 
          onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css';">
    
    <!-- Font Awesome 6.7.2 -->
    <link rel="stylesheet" href="https://cdn.jsdmirror.com/npm/@fortawesome/fontawesome-free@6.7.2/css/fontawesome.min.css"
          onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.7.2/css/fontawesome.min.css';">
    <link rel="stylesheet" href="https://cdn.jsdmirror.com/npm/@fortawesome/fontawesome-free@6.7.2/css/solid.min.css"
          onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.7.2/css/solid.min.css';">
    
    <style>
        /* 自定义样式 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        /* SweetAlert2 自定义样式 */
        .tutorial-popup .swal2-popup {
            border-radius: 16px !important;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
            border: none !important;
        }
        
        .tutorial-popup .swal2-title {
            color: #3b82f6 !important;
            font-size: 1.5rem !important;
            font-weight: 600 !important;
        }
        
        .tutorial-popup img {
            transition: transform 0.3s ease;
            border-radius: 12px;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .tutorial-popup img:hover {
            transform: scale(1.02);
        }
        
        /* 响应式调整 */
        @media (max-width: 640px) {
            .tutorial-popup .swal2-popup {
                width: 95% !important;
                margin: 0 auto !important;
            }
        }
        
        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部装饰 -->
    <div class="gradient-bg h-2"></div>
    
    <!-- 主容器 -->
    <div class="container mx-auto px-4 py-8 max-w-6xl">
        <!-- 系统标题 -->
        <div class="text-center mb-8 fade-in">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                <i class="fas fa-share-alt text-blue-600 text-2xl"></i>
            </div>
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800 mb-2">ID共享租用系统</h1>
            <p class="text-gray-600 text-sm md:text-base">安全便捷的账号共享服务</p>
        </div>
        <!-- 内容区域 -->
        <div class="fade-in">
            {% block content %}{% endblock %}
        </div>
    </div>
    
    <!-- 底部装饰 -->
    <div class="mt-16">
        <div class="gradient-bg h-1"></div>
        <div class="bg-white py-6">
            <div class="container mx-auto px-4 text-center">
                <p class="text-gray-500 text-sm">
                    <i class="fas fa-shield-alt mr-2"></i>
                    安全 · 可靠 · 专业
                </p>
            </div>
        </div>
    </div>

    <!-- JavaScript库 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/clipboard@2/dist/clipboard.min.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html> 