-- 数据库修复SQL脚本
-- 解决MySQL排序规则兼容性问题

-- 1. 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `idshare` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 2. 使用数据库
USE `idshare`;

-- 3. 备份现有数据（如果存在）
-- 这部分需要手动执行，因为SQL不能动态处理
-- 如果有重要数据，请先导出：
-- mysqldump -u idshare -p idshare > backup_$(date +%Y%m%d_%H%M%S).sql

-- 4. 删除现有表（如果存在）
DROP TABLE IF EXISTS `share_access`;
DROP TABLE IF EXISTS `share_config`;

-- 5. 创建新表（使用兼容的排序规则）
CREATE TABLE `share_access` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `uuid` VARCHAR(36) UNIQUE NOT NULL,
    `is_activated` BOOLEAN DEFAULT FALSE,
    `activated_at` TIMESTAMP NULL,
    `expires_at` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_uuid` (`uuid`),
    INDEX `idx_activated` (`is_activated`),
    INDEX `idx_expires` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `share_config` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `share_url` VARCHAR(500) NOT NULL,
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 6. 验证表结构
SHOW TABLE STATUS;
SHOW CREATE TABLE `share_access`;
SHOW CREATE TABLE `share_config`;

-- 7. 插入一些测试数据（可选）
-- INSERT INTO `share_config` (`share_url`) VALUES ('https://example.com/share');

SELECT 'Database fixed successfully!' AS message; 