#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
批量分享链接管理脚本
使用方法：
  python batch_generate.py 100                    # 生成100个新链接（默认模式）
  python batch_generate.py 50 mylinks             # 生成50个新链接到mylinks.txt
  python batch_generate.py --export 100           # 导出100个未使用的链接
  python batch_generate.py --export 50 unused     # 导出50个未使用的链接到unused.txt
"""

import sys
import os
import mysql.connector
import uuid
import datetime
from config import get_config

# 获取配置
config = get_config()

# 数据库配置 - 从环境变量获取
DB_CONFIG = {
    'host': config.MYSQL_HOST,
    'port': config.MYSQL_PORT,
    'user': config.MYSQL_USER,
    'password': config.MYSQL_PASSWORD,
    'database': config.MYSQL_DATABASE,
    'charset': config.MYSQL_CHARSET
}

def generate_links(count, filename=None):
    """生成新的分享链接并插入数据库"""
    
    if filename is None:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"new_links_{timestamp}.txt"
    
    if not filename.endswith('.txt'):
        filename += '.txt'
    
    print(f"正在生成 {count} 个新的分享链接...")
    print(f"环境: {config.FLASK_ENV}")
    print(f"有效期: {config.SHARE_EXPIRE_HOURS} 小时")
    
    try:
        # 连接数据库
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        links = []
        
        for i in range(count):
            # 生成UUID
            share_uuid = str(uuid.uuid4())
            
            # 插入数据库
            query = "INSERT INTO share_access (uuid) VALUES (%s)"
            cursor.execute(query, (share_uuid,))
            
            # 生成完整链接URL
            share_url = f"{config.BASE_URL}/share/{share_uuid}"
            links.append(share_url)
            
            print(f"进度: {i+1}/{count}")
        
        # 提交事务
        connection.commit()
        
        # 写入文件
        with open(filename, 'w', encoding='utf-8') as f:
            for link in links:
                f.write(f"{link}\n")
        
        print(f"\n✅ 成功生成 {len(links)} 个新链接")
        print(f"📁 文件保存至: {os.path.abspath(filename)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def export_unused_links(count, filename=None):
    """从数据库导出未使用的分享链接"""
    
    if filename is None:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"unused_links_{timestamp}.txt"
    
    if not filename.endswith('.txt'):
        filename += '.txt'
    
    print(f"正在导出 {count} 个未使用的分享链接...")
    print(f"环境: {config.FLASK_ENV}")
    
    try:
        # 连接数据库
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 查询未使用的UUID（未激活的）
        query = """
        SELECT uuid FROM share_access 
        WHERE is_activated = FALSE 
        ORDER BY created_at ASC 
        LIMIT %s
        """
        cursor.execute(query, (count,))
        results = cursor.fetchall()
        
        if not results:
            print("❌ 没有找到未使用的分享链接")
            return False
        
        links = []
        for row in results:
            share_uuid = row[0]
            share_url = f"{config.BASE_URL}/share/{share_uuid}"
            links.append(share_url)
        
        # 写入文件
        with open(filename, 'w', encoding='utf-8') as f:
            for link in links:
                f.write(f"{link}\n")
        
        print(f"\n✅ 成功导出 {len(links)} 个未使用链接")
        print(f"📁 文件保存至: {os.path.abspath(filename)}")
        
        if len(links) < count:
            print(f"⚠️  警告: 只找到 {len(links)} 个未使用链接，少于请求的 {count} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def show_stats():
    """显示数据库统计信息"""
    print("数据库统计信息:")
    print("-" * 50)
    
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 总链接数
        cursor.execute("SELECT COUNT(*) FROM share_access")
        total_count = cursor.fetchone()[0]
        
        # 未使用链接数
        cursor.execute("SELECT COUNT(*) FROM share_access WHERE is_activated = FALSE")
        unused_count = cursor.fetchone()[0]
        
        # 已使用链接数
        cursor.execute("SELECT COUNT(*) FROM share_access WHERE is_activated = TRUE")
        used_count = cursor.fetchone()[0]
        
        # 已过期链接数
        cursor.execute("SELECT COUNT(*) FROM share_access WHERE expires_at IS NOT NULL AND expires_at < NOW()")
        expired_count = cursor.fetchone()[0]
        
        print(f"总链接数: {total_count}")
        print(f"未使用: {unused_count}")
        print(f"已使用: {used_count}")
        print(f"已过期: {expired_count}")
        print(f"基础URL: {config.BASE_URL}")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取统计失败: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def show_config():
    """显示当前配置"""
    print("当前配置信息:")
    print("-" * 40)
    print(f"环境: {config.FLASK_ENV}")
    print(f"MySQL: {config.MYSQL_HOST}:{config.MYSQL_PORT}/{config.MYSQL_DATABASE}")
    print(f"Redis: {config.REDIS_HOST}:{config.REDIS_PORT}/{config.REDIS_DB}")
    print(f"基础URL: {config.BASE_URL}")
    print(f"分享有效期: {config.SHARE_EXPIRE_HOURS} 小时")
    print(f"每个分享分配账号数: {config.ACCOUNTS_PER_SHARE}")

def main():
    """主函数"""
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python batch_generate.py <数量> [文件名]        # 生成新链接（默认）")
        print("  python batch_generate.py --export <数量> [文件名] # 导出未使用链接")
        print("  python batch_generate.py --stats               # 显示统计信息")
        print("  python batch_generate.py --config              # 显示配置")
        print("\n示例:")
        print("  python batch_generate.py 100                   # 生成100个新链接")
        print("  python batch_generate.py 50 mylinks            # 生成50个新链接到mylinks.txt")
        print("  python batch_generate.py --export 100          # 导出100个未使用链接")
        print("  python batch_generate.py --export 50 unused    # 导出50个未使用链接到unused.txt")
        sys.exit(1)
    
    # 显示配置
    if sys.argv[1] == '--config':
        show_config()
        return
    
    # 显示统计
    if sys.argv[1] == '--stats':
        show_stats()
        return
    
    # 导出模式
    if sys.argv[1] == '--export':
        if len(sys.argv) < 3:
            print("❌ 导出模式需要指定数量")
            print("使用方法: python batch_generate.py --export <数量> [文件名]")
            sys.exit(1)
        
        try:
            count = int(sys.argv[2])
            if count <= 0:
                print("❌ 数量必须大于0")
                sys.exit(1)
            
            filename = sys.argv[3] if len(sys.argv) > 3 else None
            
            # 导出未使用链接
            success = export_unused_links(count, filename)
            
            if success:
                print("🎉 导出完成！")
            else:
                sys.exit(1)
                
        except ValueError:
            print("❌ 请输入有效的数量")
            sys.exit(1)
        except KeyboardInterrupt:
            print("\n\n⏹️  操作已取消")
            sys.exit(0)
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            sys.exit(1)
        
        return
    
    # 默认生成模式
    try:
        count = int(sys.argv[1])
        if count <= 0:
            print("❌ 数量必须大于0")
            sys.exit(1)
            
        filename = sys.argv[2] if len(sys.argv) > 2 else None
        
        # 安全确认
        max_batch = config.MAX_LINKS_PER_BATCH
        if count > max_batch:
            print(f"⚠️  请求生成数量({count})超过配置限制({max_batch})")
            confirm = input(f"将要生成 {count} 个新链接，确认继续？(y/N): ")
            if confirm.lower() != 'y':
                print("已取消")
                sys.exit(0)
        elif count > 1000:
            confirm = input(f"⚠️  将要生成 {count} 个新链接，确认继续？(y/N): ")
            if confirm.lower() != 'y':
                print("已取消")
                sys.exit(0)
        
        # 生成新链接
        success = generate_links(count, filename)
        
        if success:
            print("🎉 生成完成！")
        else:
            sys.exit(1)
            
    except ValueError:
        print("❌ 请输入有效的数量")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⏹️  操作已取消")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main() 