{% extends "admin_base.html" %}

{% block title %}分享链接管理 - ID共享租用系统{% endblock %}
{% block page_title %}分享链接管理{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/shares.css') }}">
{% endblock %}

{% block main_content %}
<!-- 快速操作栏 -->
<div class="mb-6 flex flex-wrap gap-4">
    <button onclick="refreshShares()" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-md">
        <i class="fas fa-sync-alt mr-2"></i>刷新数据
    </button>
    <button onclick="showBatchExpireModal()" class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors shadow-md">
        <i class="fas fa-ban mr-2"></i>批量过期
    </button>
</div>

<!-- 筛选器 -->
<div class="bg-white rounded-xl shadow-lg p-6 mb-6 filter-container">
    <div class="flex flex-wrap items-center gap-4">
        <div class="flex items-center space-x-2">
            <label class="text-sm font-medium text-gray-700">状态筛选：</label>
            <select id="statusFilter" onchange="filterShares()" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="all">全部</option>
                <option value="active">已激活未过期</option>
                <option value="expired">已过期</option>
                <option value="inactive">未激活</option>
            </select>
        </div>
        
        <div class="flex items-center space-x-2">
            <label class="text-sm font-medium text-gray-700">时间范围：</label>
            <select id="timeFilter" onchange="filterShares()" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="all">全部时间</option>
                <option value="today">今天</option>
                <option value="week">本周</option>
                <option value="month">本月</option>
            </select>
        </div>
        
        <div class="flex items-center space-x-2">
            <input type="text" id="searchInput" placeholder="搜索UUID..." 
                   onkeyup="filterShares()" 
                   class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        
        <div class="ml-auto">
            <span class="text-sm text-gray-600">
                总计：<span id="totalCount">0</span> | 
                显示：<span id="visibleCount">0</span>
            </span>
        </div>
    </div>
</div>

<!-- 分享链接列表 -->
<div class="bg-white rounded-xl shadow-lg overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">分享链接列表</h3>
    </div>
    
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" class="rounded">
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UUID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">激活时间</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">过期时间</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">剩余时间</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
            </thead>
            <tbody id="sharesTableBody" class="bg-white divide-y divide-gray-200">
                <!-- 数据将通过JavaScript动态加载 -->
            </tbody>
        </table>
    </div>
    
    <!-- 分页 -->
    <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
        <div class="text-sm text-gray-700">
            显示第 <span id="pageStart">0</span> - <span id="pageEnd">0</span> 条，共 <span id="pageTotal">0</span> 条
        </div>
        <div class="flex space-x-2">
            <button onclick="previousPage()" id="prevBtn" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50">
                上一页
            </button>
            <span id="pageInfo" class="px-3 py-1 text-sm text-gray-700">第 1 页，共 1 页</span>
            <button onclick="nextPage()" id="nextBtn" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50">
                下一页
            </button>
        </div>
    </div>
</div>

<!-- 批量过期确认模态框 -->
<div id="batchExpireModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">批量过期确认</h3>
                <p class="text-gray-600 mb-4">您确定要让选中的分享链接立即过期吗？此操作不可撤销。</p>
                <p class="text-sm text-red-600 mb-4">已选择 <span id="selectedCount">0</span> 个链接</p>
                <div class="flex justify-end space-x-3">
                    <button onclick="closeBatchExpireModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded hover:bg-gray-300">
                        取消
                    </button>
                    <button onclick="confirmBatchExpire()" class="px-4 py-2 text-white bg-red-600 rounded hover:bg-red-700">
                        确认过期
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 单个过期确认模态框 -->
<div id="singleExpireModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">过期确认</h3>
                <p class="text-gray-600 mb-4">您确定要让此分享链接立即过期吗？</p>
                <p class="text-sm text-gray-500 mb-4">UUID: <span id="expireUuid" class="font-mono"></span></p>
                <div class="flex justify-end space-x-3">
                    <button onclick="closeSingleExpireModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded hover:bg-gray-300">
                        取消
                    </button>
                    <button onclick="confirmSingleExpire()" class="px-4 py-2 text-white bg-red-600 rounded hover:bg-red-700">
                        确认过期
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let allShares = [];
let filteredShares = [];
let currentPage = 1;
const itemsPerPage = 20;
let selectedUuids = new Set();
let currentExpireUuid = null;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadShares();
    
    // 设置自动刷新
    setInterval(loadShares, 30000); // 30秒刷新一次
});

// 加载分享链接数据
async function loadShares() {
    try {
        const response = await fetch('/api/shares/list');
        const data = await response.json();
        
        if (data.success) {
            allShares = data.shares;
            filterShares();
        } else {
            showAlert('获取分享链接失败: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('加载分享链接失败:', error);
        showAlert('网络错误，请稍后重试', 'error');
    }
}

// 刷新数据
function refreshShares() {
    loadShares();
    showAlert('数据已刷新', 'success');
}
</script>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/shares.js') }}"></script>
{% endblock %}
