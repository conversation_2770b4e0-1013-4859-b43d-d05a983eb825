{% extends "admin_base.html" %}

{% block content %}
<div class="min-h-screen flex items-center justify-center relative overflow-hidden" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <!-- 动态背景元素 -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-10 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-white opacity-10 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
    </div>
    
    <div class="relative z-10 w-full max-w-md px-6">
        <!-- 登录卡片 -->
        <div class="bg-white/95 backdrop-blur-lg rounded-3xl shadow-2xl p-8 transform transition-all duration-500 hover:scale-105" id="loginCard">
            <!-- 头部 -->
            <div class="text-center mb-8">
                <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <i class="fas fa-shield-alt text-white text-2xl"></i>
                </div>
                <h2 class="text-3xl font-bold text-gray-900 mb-2">管理员登录</h2>
                <p class="text-gray-600">请输入您的管理员凭据以访问系统</p>
            </div>

            <!-- 登录表单 -->
            <form class="space-y-6" action="{{ url_for('admin_login') }}" method="POST">
                <!-- 用户名输入 -->
                <div class="space-y-2">
                    <label for="username" class="block text-sm font-medium text-gray-700">
                        <i class="fas fa-user mr-2 text-blue-500"></i>用户名
                    </label>
                    <div class="relative">
                        <input type="text" 
                               id="username" 
                               name="username" 
                               required
                               autocomplete="username"
                               placeholder="请输入管理员用户名"
                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 pl-12 bg-gray-50 focus:bg-white">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-user text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 密码输入 -->
                <div class="space-y-2">
                    <label for="password" class="block text-sm font-medium text-gray-700">
                        <i class="fas fa-lock mr-2 text-blue-500"></i>密码
                    </label>
                    <div class="relative">
                        <input type="password" 
                               id="password" 
                               name="password" 
                               required
                               autocomplete="current-password"
                               placeholder="请输入管理员密码"
                               class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 pl-12 pr-12 bg-gray-50 focus:bg-white">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <button type="button" 
                                onclick="togglePassword()" 
                                class="absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 transition-colors">
                            <i class="fas fa-eye text-gray-400" id="toggleIcon"></i>
                        </button>
                    </div>
                </div>

                <!-- 记住我选项 -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="remember" 
                               name="remember" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="remember" class="ml-2 block text-sm text-gray-700">
                            记住我
                        </label>
                    </div>
                </div>

                <!-- 登录按钮 -->
                <button type="submit" 
                        class="w-full flex justify-center py-3 px-4 border border-transparent rounded-xl shadow-lg text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        id="loginBtn">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    <span id="loginText">登录管理面板</span>
                </button>
            </form>

            <!-- 底部信息 -->
            <div class="mt-8 text-center">
                <p class="text-xs text-gray-500 flex items-center justify-center">
                    <i class="fas fa-shield-alt mr-2"></i>
                    安全登录 · 数据加密传输
                </p>
            </div>
        </div>

        <!-- 返回首页链接 -->
        <div class="text-center mt-6">
            <a href="/" class="inline-flex items-center text-white/80 hover:text-white transition duration-200 text-sm">
                <i class="fas fa-arrow-left mr-2"></i>
                返回首页
            </a>
        </div>
    </div>
</div>

<!-- 错误消息数据 -->
<script type="application/json" id="messageData">
{
    "hasError": {% with messages = get_flashed_messages(with_categories=true) %}{% if messages %}{% for category, message in messages %}{% if category == 'danger' %}true{% else %}false{% endif %}{% endfor %}{% else %}false{% endif %}{% endwith %}
}
</script>
{% endblock %}

{% block scripts %}
<style>
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }
    
    .animate-float {
        animation: float 6s ease-in-out infinite;
    }
    
    .input-focus-effect {
        transition: all 0.3s ease;
    }
    
    .input-focus-effect:focus {
        transform: scale(1.02);
    }
</style>

<script>
// 切换密码显示/隐藏
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash text-gray-400';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye text-gray-400';
    }
}

// 表单提交动画
document.querySelector('form').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('loginBtn');
    const loginText = document.getElementById('loginText');
    
    submitBtn.disabled = true;
    loginText.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>登录中...';
    submitBtn.classList.add('opacity-75');
});

// 输入框动画效果
document.querySelectorAll('input').forEach(input => {
    input.addEventListener('focus', function() {
        this.classList.add('input-focus-effect');
        this.parentElement.classList.add('transform', 'scale-105');
    });
    
    input.addEventListener('blur', function() {
        this.classList.remove('input-focus-effect');
        this.parentElement.classList.remove('transform', 'scale-105');
    });
});

// 页面加载动画和错误处理
document.addEventListener('DOMContentLoaded', function() {
    const card = document.getElementById('loginCard');
    if (card) {
        card.classList.add('animate-float');
    }
    
    // 检查错误消息并添加动画
    try {
        const messageData = JSON.parse(document.getElementById('messageData').textContent);
        if (messageData.hasError && card) {
            card.classList.add('animate-bounce');
            setTimeout(() => {
                card.classList.remove('animate-bounce');
            }, 1000);
        }
    } catch (e) {
        console.log('No message data found');
    }
});
</script>
{% endblock %} 