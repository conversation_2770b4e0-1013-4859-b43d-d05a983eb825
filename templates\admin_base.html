<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}管理员面板 - ID共享租用系统{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdmirror.com/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" 
          onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css';">
    
    <!-- Font Awesome 6.7.2 -->
    <link rel="stylesheet" href="https://cdn.jsdmirror.com/npm/@fortawesome/fontawesome-free@6.7.2/css/fontawesome.min.css"
          onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.7.2/css/fontawesome.min.css';">
    <link rel="stylesheet" href="https://cdn.jsdmirror.com/npm/@fortawesome/fontawesome-free@6.7.2/css/solid.min.css"
          onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.7.2/css/solid.min.css';">
    
    <style>
        /* 管理员界面专用样式 */
        .admin-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .admin-card {
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .admin-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .sidebar {
            background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%);
        }
        
        .sidebar-item {
            transition: all 0.2s ease;
        }
        
        .sidebar-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        
        .sidebar-item.active {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
        }
        
        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- 统一的内容块 -->
    {% block content %}
    <!-- 默认管理页面布局 -->
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 sidebar transform -translate-x-full lg:translate-x-0 transition-transform duration-200 ease-in-out" id="sidebar">
        <!-- 侧边栏头部 -->
        <div class="flex items-center justify-center h-16 border-b border-blue-800">
            <h2 class="text-white text-xl font-bold">
                <i class="fas fa-cog mr-2"></i>
                管理面板
            </h2>
        </div>
        
        <!-- 侧边栏菜单 -->
        <nav class="mt-5 px-2">
            <a href="/{{ config.ADMIN_PATH }}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium text-white mb-1">
                <i class="fas fa-tachometer-alt mr-3 h-5 w-5"></i>
                仪表盘
            </a>
            
            <a href="/{{ config.ADMIN_PATH }}/accounts" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium text-white mb-1">
                <i class="fas fa-users mr-3 h-5 w-5"></i>
                账号管理
            </a>
            
            <a href="/{{ config.ADMIN_PATH }}/shares" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium text-white mb-1">
                <i class="fas fa-share-alt mr-3 h-5 w-5"></i>
                分享管理
            </a>
            
            <a href="/{{ config.ADMIN_PATH }}/settings" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium text-white mb-1">
                <i class="fas fa-cog mr-3 h-5 w-5"></i>
                系统设置
            </a>
            
            <div class="border-t border-blue-800 mt-5 pt-5">
                <a href="/{{ config.ADMIN_PATH }}/logout" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium text-red-300 hover:text-white">
                    <i class="fas fa-sign-out-alt mr-3 h-5 w-5"></i>
                    退出登录
                </a>
            </div>
        </nav>
    </div>
    
    <!-- 主内容区域 -->
    <div class="lg:pl-64">
        <!-- 顶部导航栏 -->
        <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <!-- 移动端菜单按钮 -->
                        <button type="button" class="lg:hidden -ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900" onclick="toggleSidebar()">
                            <i class="fas fa-bars h-6 w-6"></i>
                        </button>
                        
                        <h1 class="ml-4 text-xl font-semibold text-gray-900">{% block page_title %}管理面板{% endblock %}</h1>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600">
                            <i class="fas fa-user-shield mr-1"></i>
                            管理员
                        </span>
                        <a href="/{{ config.ADMIN_PATH }}/logout" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-sign-out-alt"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <main class="py-6">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="mb-4 p-4 rounded-lg {% if category == 'success' %}bg-green-50 border border-green-200 text-green-800{% elif category == 'danger' %}bg-red-50 border border-red-200 text-red-800{% elif category == 'warning' %}bg-yellow-50 border border-yellow-200 text-yellow-800{% else %}bg-blue-50 border border-blue-200 text-blue-800{% endif %}">
                                <div class="flex items-center">
                                    <i class="fas {% if category == 'success' %}fa-check-circle{% elif category == 'danger' %}fa-exclamation-circle{% elif category == 'warning' %}fa-exclamation-triangle{% else %}fa-info-circle{% endif %} mr-2"></i>
                                    {{ message }}
                                </div>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                {% block main_content %}{% endblock %}
            </div>
        </main>
    </div>
    {% endblock %}

    <!-- JavaScript库 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // 切换侧边栏显示/隐藏
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.toggle('-translate-x-full');
            }
        }
        
        // 高亮当前页面菜单项
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const menuItems = document.querySelectorAll('.sidebar-item');
            
            menuItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    item.classList.add('active');
                }
            });
        });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html> 