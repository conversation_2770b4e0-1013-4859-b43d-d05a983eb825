# 固定账号分配功能使用指南

## 功能概述

系统现已支持**每个分享链接固定分配指定数量账号**的功能，确保同一个分享链接每次访问都返回相同的账号信息。

## 主要特性

### 1. 固定分配机制
- 每个分享链接在首次激活时，会被分配固定的账号
- 同一链接后续访问，始终返回相同的账号
- 不同的分享链接，会分配不同的账号组合

### 2. 可配置账号数量
- 通过`.env`文件中的`ACCOUNTS_PER_SHARE`参数控制
- 默认每个链接分配4个账号
- 可根据需要调整为其他数值

### 3. 智能分配算法
- 使用分享链接UUID作为随机种子
- 确保相同UUID每次都产生相同的账号组合
- 在账号池充足时，不同链接获得不同的账号

## 配置方法

### 1. 环境配置

在`.env`文件中设置账号分配数量：

```bash
# 系统配置
ACCOUNTS_PER_SHARE=4  # 每个分享链接分配的账号数量
```

支持的取值范围：
- 最小值：1
- 最大值：账号池总数
- 推荐值：4-6

### 2. 数据库升级

运行数据库迁移脚本：

```bash
# 方法1：直接运行SQL文件
mysql -u username -p database < add_assigned_accounts_column.sql

# 方法2：使用Python脚本验证
python verify_db_changes.py
```

## 工作原理

### 1. 账号ID生成
- 系统为每个账号自动生成唯一ID
- ID格式：`{username}_{index}`
- 存储在Redis账号池中

### 2. 分配流程
```
用户访问分享链接
       ↓
   检查是否已激活
       ↓
   已激活：获取已分配的账号ID → 根据ID获取账号详情
       ↓
  未激活：分配新账号ID → 保存到数据库 → 激活链接
```

### 3. 数据存储
- **Redis**：存储完整账号池信息，包含账号ID
- **MySQL**：存储分享链接与分配账号的对应关系

## 使用示例

### 1. 基本使用

```python
# 分享链接激活时自动分配账号
@app.route('/share/<share_uuid>')
def share_accounts(share_uuid):
    # 系统自动处理账号分配
    # 用户看到固定的4个账号
```

### 2. 配置不同数量

```bash
# 分配2个账号
ACCOUNTS_PER_SHARE=2

# 分配6个账号  
ACCOUNTS_PER_SHARE=6
```

### 3. 测试验证

```bash
# 运行测试脚本
python test_fixed_accounts.py

# 查看分配结果
# 同一UUID多次运行，结果相同
# 不同UUID运行，结果不同
```

## API变更

### 1. 新增方法

**RedisManager**:
- `get_accounts_by_ids(account_ids)` - 根据ID列表获取账号
- `assign_accounts_to_share(share_uuid, count)` - 为链接分配账号

**DatabaseManager**:
- `set_assigned_accounts(share_uuid, account_ids)` - 保存分配记录
- `get_assigned_accounts(share_uuid)` - 获取分配记录
- `activate_share_with_accounts(share_uuid, account_ids)` - 激活并分配

### 2. 修改的路由
- `/share/<share_uuid>` - 现在返回固定分配的账号

## 数据库变更

### 新增字段

`share_access`表新增字段：
```sql
assigned_accounts TEXT DEFAULT NULL COMMENT '分配给该链接的账号ID列表，JSON格式存储'
```

### 数据格式

```json
{
    "assigned_accounts": ["user1_0", "user2_1", "user3_2", "user4_3"]
}
```

## 兼容性说明

### 1. 向后兼容
- 现有的分享链接不受影响
- 首次访问时会自动分配账号
- 已激活的链接会根据新逻辑重新分配

### 2. 升级步骤
1. 停止应用服务
2. 运行数据库迁移脚本
3. 更新配置文件
4. 重启应用服务

## 监控和维护

### 1. 日志监控

系统会记录以下关键信息：
```
[INFO] 为分享链接 uuid 分配了 4 个账号
[INFO] 激活分享链接并分配账号: uuid, 4 个账号
[WARNING] 分享链接 uuid 的分配账号不足，重新分配
```

### 2. 故障处理

**账号池不足时**：
- 系统会分配所有可用账号
- 记录警告日志
- 建议增加账号源

**分配记录丢失时**：
- 系统会自动重新分配
- 使用相同算法确保一致性

## 性能优化

### 1. 缓存策略
- Redis存储完整账号池
- MySQL只存储分配关系
- 减少跨系统查询

### 2. 查询优化
- 为`assigned_accounts`字段添加索引
- 批量获取账号信息
- 避免N+1查询问题

## 测试验证

运行测试脚本验证功能：
```bash
python test_fixed_accounts.py
```

预期输出：
- 配置信息正确显示
- 同一UUID分配结果一致
- 不同UUID分配结果不同

## 故障排除

### 常见问题

1. **账号分配不一致**
   - 检查UUID是否正确
   - 验证随机种子算法
   - 确认账号池数据完整

2. **数据库字段缺失**
   - 运行迁移脚本
   - 检查表结构

3. **配置不生效**
   - 确认`.env`文件格式
   - 重启应用服务
   - 检查配置读取逻辑

### 调试命令

```bash
# 检查配置
python -c "from config import get_config; print(get_config().ACCOUNTS_PER_SHARE)"

# 验证数据库
python verify_db_changes.py

# 测试分配逻辑
python test_fixed_accounts.py
```

## 未来扩展

### 可能的改进方向

1. **账号质量权重**：根据账号状态调整分配权重
2. **地域分配**：根据用户地理位置分配相应区域账号
3. **动态调整**：根据使用情况动态调整分配数量
4. **分配策略**：支持多种分配算法（轮询、负载均衡等）

---

## 总结

固定账号分配功能为系统提供了更稳定和可预测的用户体验，同时保持了高度的灵活性和可配置性。通过合理的配置和监控，可以满足各种业务场景的需求。 