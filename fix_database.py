#!/usr/bin/env python3
"""
数据库修复脚本
解决MySQL排序规则兼容性问题
"""

import mysql.connector
from mysql.connector import Error
from config import get_config
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_mysql_connection():
    """获取MySQL连接（不指定数据库）"""
    config_class = get_config()
    
    # 连接配置（不包含数据库名）
    connection_config = {
        'host': config_class.MYSQL_HOST,
        'port': config_class.MYSQL_PORT,
        'user': config_class.MYSQL_USER,
        'password': config_class.MYSQL_PASSWORD,
        'charset': 'utf8mb4',
        'use_unicode': True,
        'autocommit': False
    }
    
    try:
        connection = mysql.connector.connect(**connection_config)
        logger.info("MySQL连接成功")
        return connection
    except Error as e:
        logger.error(f"MySQL连接失败: {e}")
        return None

def fix_database():
    """修复数据库问题"""
    config_class = get_config()
    connection = get_mysql_connection()
    
    if not connection:
        logger.error("无法连接到MySQL服务器")
        return False
    
    try:
        cursor = connection.cursor()
        
        # 1. 创建数据库（如果不存在）
        database_name = config_class.MYSQL_DATABASE
        logger.info(f"检查数据库: {database_name}")
        
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{database_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci")
        logger.info(f"数据库 {database_name} 创建/验证成功")
        
        # 2. 使用数据库
        cursor.execute(f"USE `{database_name}`")
        
        # 3. 检查并修复现有表
        cursor.execute("SHOW TABLES")
        existing_tables = [table[0] for table in cursor.fetchall()]
        
        if existing_tables:
            logger.info(f"发现现有表: {existing_tables}")
            
            # 备份现有数据
            backup_data = {}
            
            if 'share_access' in existing_tables:
                logger.info("备份 share_access 表数据")
                cursor.execute("SELECT * FROM share_access")
                backup_data['share_access'] = cursor.fetchall()
                cursor.execute("DESCRIBE share_access")
                backup_data['share_access_columns'] = [col[0] for col in cursor.fetchall()]
            
            if 'share_config' in existing_tables:
                logger.info("备份 share_config 表数据")
                cursor.execute("SELECT * FROM share_config")
                backup_data['share_config'] = cursor.fetchall()
                cursor.execute("DESCRIBE share_config")
                backup_data['share_config_columns'] = [col[0] for col in cursor.fetchall()]
            
            # 删除现有表
            for table in existing_tables:
                logger.info(f"删除表: {table}")
                cursor.execute(f"DROP TABLE IF EXISTS `{table}`")
        
        # 4. 创建新表（使用兼容的排序规则）
        logger.info("创建新的数据表")
        
        # 创建UUID访问控制表
        create_share_table = """
        CREATE TABLE IF NOT EXISTS share_access (
            id INT AUTO_INCREMENT PRIMARY KEY,
            uuid VARCHAR(36) UNIQUE NOT NULL,
            is_activated BOOLEAN DEFAULT FALSE,
            activated_at TIMESTAMP NULL,
            expires_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_uuid (uuid),
            INDEX idx_activated (is_activated),
            INDEX idx_expires (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        """
        
        # 创建分享链接配置表
        create_config_table = """
        CREATE TABLE IF NOT EXISTS share_config (
            id INT AUTO_INCREMENT PRIMARY KEY,
            share_url VARCHAR(500) NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        """
        
        cursor.execute(create_share_table)
        logger.info("share_access 表创建成功")
        
        cursor.execute(create_config_table)
        logger.info("share_config 表创建成功")
        
        # 5. 恢复备份数据
        if 'backup_data' in locals() and backup_data:
            if 'share_access' in backup_data and backup_data['share_access']:
                logger.info("恢复 share_access 表数据")
                columns = backup_data['share_access_columns']
                placeholders = ', '.join(['%s'] * len(columns))
                insert_query = f"INSERT INTO share_access ({', '.join(columns)}) VALUES ({placeholders})"
                
                for row in backup_data['share_access']:
                    try:
                        cursor.execute(insert_query, row)
                    except Error as e:
                        logger.warning(f"恢复数据行失败: {e}")
                
                logger.info(f"恢复了 {len(backup_data['share_access'])} 条 share_access 记录")
            
            if 'share_config' in backup_data and backup_data['share_config']:
                logger.info("恢复 share_config 表数据")
                columns = backup_data['share_config_columns']
                placeholders = ', '.join(['%s'] * len(columns))
                insert_query = f"INSERT INTO share_config ({', '.join(columns)}) VALUES ({placeholders})"
                
                for row in backup_data['share_config']:
                    try:
                        cursor.execute(insert_query, row)
                    except Error as e:
                        logger.warning(f"恢复数据行失败: {e}")
                
                logger.info(f"恢复了 {len(backup_data['share_config'])} 条 share_config 记录")
        
        # 6. 提交更改
        connection.commit()
        logger.info("数据库修复完成！")
        
        # 7. 验证表结构
        cursor.execute("SHOW TABLE STATUS")
        tables_info = cursor.fetchall()
        for table_info in tables_info:
            logger.info(f"表 {table_info[0]} - 排序规则: {table_info[14]}")
        
        return True
        
    except Error as e:
        logger.error(f"数据库修复失败: {e}")
        if connection:
            connection.rollback()
        return False
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()
            logger.info("数据库连接已关闭")

def test_connection():
    """测试修复后的连接"""
    logger.info("测试数据库连接...")
    
    config_class = get_config()
    
    # 完整的连接配置
    mysql_config = {
        'host': config_class.MYSQL_HOST,
        'port': config_class.MYSQL_PORT,
        'user': config_class.MYSQL_USER,
        'password': config_class.MYSQL_PASSWORD,
        'database': config_class.MYSQL_DATABASE,
        'charset': config_class.MYSQL_CHARSET,
        'collation': getattr(config_class, 'MYSQL_COLLATION', 'utf8mb4_general_ci'),
        'use_unicode': True,
        'autocommit': False
    }
    
    try:
        connection = mysql.connector.connect(**mysql_config)
        cursor = connection.cursor()
        
        # 测试查询
        cursor.execute("SELECT COUNT(*) FROM share_access")
        share_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM share_config")
        config_count = cursor.fetchone()[0]
        
        logger.info(f"连接测试成功！")
        logger.info(f"share_access 表记录数: {share_count}")
        logger.info(f"share_config 表记录数: {config_count}")
        
        return True
        
    except Error as e:
        logger.error(f"连接测试失败: {e}")
        return False
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    print("=" * 50)
    print("数据库修复脚本")
    print("=" * 50)
    
    # 修复数据库
    if fix_database():
        print("\n✅ 数据库修复成功！")
        
        # 测试连接
        if test_connection():
            print("✅ 连接测试通过！")
            print("\n现在可以正常使用系统了。")
        else:
            print("❌ 连接测试失败，请检查配置。")
    else:
        print("❌ 数据库修复失败！")
        print("\n请检查以下项目：")
        print("1. MySQL服务是否正常运行")
        print("2. 数据库连接配置是否正确")
        print("3. 用户是否有足够的权限")
        print("4. 防火墙设置是否正确") 