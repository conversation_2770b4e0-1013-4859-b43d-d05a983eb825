from flask import Flask, request, jsonify, render_template, redirect, url_for, session, flash
import redis
import mysql.connector
from mysql.connector import Error
import requests
from bs4 import BeautifulSoup
import json
import uuid
import datetime
from apscheduler.schedulers.background import BackgroundScheduler
import logging
import os
from werkzeug.utils import secure_filename
from functools import wraps
from config import get_config
import re
import pytz
from datetime import timezone, timedelta

# 获取配置
config_class = get_config()

# 应用配置
app = Flask(__name__)
app.secret_key = config_class.SECRET_KEY

# 添加模板上下文处理器
@app.context_processor
def inject_config():
    """向模板注入配置变量"""
    return dict(config=config_class)

# 配置日志 - 移到前面，避免logger未定义错误
log_level = getattr(logging, config_class.LOG_LEVEL.upper(), logging.INFO)
logging.basicConfig(
    level=log_level,
    format='%(asctime)s %(levelname)s %(name)s: %(message)s',
    handlers=[
        logging.FileHandler(config_class.LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 时区配置
def get_local_timezone():
    """获取本地时区对象"""
    try:
        return pytz.timezone(config_class.TIMEZONE)
    except Exception as e:
        logger.warning(f"时区配置错误 {config_class.TIMEZONE}, 使用默认时区: {e}")
        return pytz.timezone('Asia/Shanghai')

def get_local_now():
    """获取当前本地时间"""
    local_tz = get_local_timezone()
    return datetime.datetime.now(local_tz)

def localize_datetime(dt):
    """将naive datetime转换为本地时区"""
    if dt is None:
        return None
    
    local_tz = get_local_timezone()
    
    # 如果已经有时区信息，转换到本地时区
    if dt.tzinfo is not None:
        return dt.astimezone(local_tz)
    
    # 如果是naive datetime，假设是UTC时间，然后转换到本地时区
    utc_dt = pytz.UTC.localize(dt)
    return utc_dt.astimezone(local_tz)

def format_local_time(dt, format_string='%Y-%m-%d %H:%M:%S'):
    """格式化本地时间显示"""
    if dt is None:
        return None
    
    localized_dt = localize_datetime(dt)
    return localized_dt.strftime(format_string)

# 初始化时区
local_tz = get_local_timezone()
logger.info(f"系统时区设置为: {config_class.TIMEZONE}")

# 添加Jinja2过滤器用于时间格式化
@app.template_filter('local_time')
def local_time_filter(datetime_str):
    """Jinja2过滤器：将ISO时间字符串转换为本地时区显示"""
    try:
        if not datetime_str:
            return ''
        
        # 如果是字符串，尝试解析
        if isinstance(datetime_str, str):
            dt = datetime.datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
        else:
            dt = datetime_str
        
        # 转换为本地时区并格式化
        local_dt = localize_datetime(dt)
        return local_dt.strftime('%Y-%m-%d %H:%M:%S')
    except Exception as e:
        logger.warning(f"时间格式化失败: {e}")
        return str(datetime_str) if datetime_str else ''

@app.template_filter('timezone_name')
def timezone_name_filter(value):
    """Jinja2过滤器：返回当前配置的时区名称"""
    return config_class.TIMEZONE

# 添加UUID验证函数
def validate_uuid(uuid_string):
    """验证UUID格式是否正确，防止SQL注入"""
    import re
    uuid_pattern = re.compile(
        r'^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$',
        re.IGNORECASE
    )
    return bool(uuid_pattern.match(uuid_string))

def sanitize_input(input_string, max_length=500):
    """输入数据清理和验证"""
    if not input_string:
        return ""
    
    # 长度限制
    if len(input_string) > max_length:
        return input_string[:max_length]
    
    # 检查危险的SQL关键字（基础防护）
    dangerous_keywords = [
        'DROP', 'DELETE', 'INSERT', 'UPDATE', 'SELECT', 'UNION', 'ALTER', 
        'CREATE', 'TRUNCATE', 'EXEC', 'EXECUTE', 'SCRIPT', 'JAVASCRIPT'
    ]
    
    upper_input = input_string.upper()
    for keyword in dangerous_keywords:
        if keyword in upper_input:
            logger.warning(f"检测到可疑输入包含关键字: {keyword}")
            # 可以选择清理或拒绝
            input_string = input_string.replace(keyword.lower(), '').replace(keyword.upper(), '')
    
    return input_string.strip()

# Redis配置
redis_config = {
    'host': config_class.REDIS_HOST,
    'port': config_class.REDIS_PORT,
    'db': config_class.REDIS_DB,
    'decode_responses': True
}

# 如果有Redis密码，添加密码配置
if config_class.REDIS_PASSWORD:
    redis_config['password'] = config_class.REDIS_PASSWORD

redis_client = redis.Redis(**redis_config)

# MySQL配置
mysql_config = {
    'host': config_class.MYSQL_HOST,
    'port': config_class.MYSQL_PORT,
    'user': config_class.MYSQL_USER,
    'password': config_class.MYSQL_PASSWORD,
    'database': config_class.MYSQL_DATABASE,
    'charset': config_class.MYSQL_CHARSET,
    'collation': getattr(config_class, 'MYSQL_COLLATION', 'utf8mb4_general_ci'),
    'use_unicode': True,
    'autocommit': False
}

class AccountExtractor:
    """账号信息提取器"""
    
    def __init__(self, share_url):
        self.share_url = share_url
        
    def extract_accounts(self):
        """从分享链接提取账号信息"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(self.share_url, timeout=10, headers=headers)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            accounts = []
            
            # 查找所有账号卡片
            cards = soup.find_all('div', class_='card')
            
            for card in cards:
                account_info = {}
                
                # 提取用户名 - 改进逻辑以处理包含区域标签的情况
                username_elements = [
                    card.find('h3', class_='card-text'),
                    card.find('h3'),
                    card.find('p', class_='card-text'),
                    card.find('div', class_='card-text')
                ]
                
                for username_element in username_elements:
                    if username_element:
                        # 方法1：直接获取文本节点，排除badge标签内容
                        username_text = ""
                        region_text = ""
                        
                        for content in username_element.contents:
                            if hasattr(content, 'name'):
                                # 这是一个标签，检查是否是badge
                                if content.name == 'span' and 'badge' in content.get('class', []):
                                    # 提取区域信息
                                    region_text = content.get_text(strip=True)
                                    continue  # 跳过badge标签，不加入用户名
                                else:
                                    username_text += content.get_text(strip=True)
                            else:
                                # 这是纯文本节点
                                username_text += str(content).strip()
                        
                        # 清理和验证用户名
                        username_text = username_text.strip()
                        if username_text and username_text not in ['账号信息', '账户信息', '用户信息']:
                            # 进一步清理：移除可能残留的空格和特殊字符
                            username_clean = username_text.strip()
                            
                            # 如果仍然包含空格，可能是格式问题，取第一个词作为用户名
                            if ' ' in username_clean:
                                # 检查是否是"用户名 区域"格式
                                parts = username_clean.split()
                                # 通常用户名是第一部分，区域信息在后面
                                if len(parts) >= 1:
                                    # 如果没有从badge提取到区域信息，从文本中获取
                                    if not region_text and len(parts) > 1:
                                        region_text = ' '.join(parts[1:])
                                    username_clean = parts[0]
                            
                            # 额外清理：移除常见的区域标识符（仅从用户名中移除）
                            # 常见的区域格式：US, CN, 美国, 中国, (US), [CN] 等
                            # 移除括号内的内容（通常是区域代码）
                            
                            # 如果没有区域信息，尝试从用户名中提取
                            if not region_text:
                                # 提取括号内容作为区域
                                bracket_match = re.search(r'\(([^)]*)\)', username_clean)
                                if bracket_match:
                                    region_text = bracket_match.group(1)
                                else:
                                    square_match = re.search(r'\[([^\]]*)\]', username_clean)
                                    if square_match:
                                        region_text = square_match.group(1)
                                    else:
                                        # 尝试匹配常见区域后缀
                                        region_match = re.search(r'\s+(US|CN|美国|中国|日本|JP|KR|韩国|英国|UK|德国|DE|法国|FR)$', username_clean, flags=re.IGNORECASE)
                                        if region_match:
                                            region_text = region_match.group(1)
                            
                            # 清理用户名，移除区域信息
                            username_clean = re.sub(r'\([^)]*\)', '', username_clean).strip()
                            username_clean = re.sub(r'\[[^\]]*\]', '', username_clean).strip()
                            # 移除常见的区域后缀
                            username_clean = re.sub(r'\s+(US|CN|美国|中国|日本|JP|KR|韩国|英国|UK|德国|DE|法国|FR)$', '', username_clean, flags=re.IGNORECASE).strip()
                            
                            account_info['username'] = username_clean
                            
                            # 如果提取到区域信息，添加到账户信息中
                            if region_text:
                                account_info['region_display'] = region_text.strip()
                            
                            break
                
                # 提取状态 - 查找状态标识
                status_badges = card.find_all('span', class_='badge')
                account_info['status'] = 'abnormal'  # 默认异常
                
                for badge in status_badges:
                    badge_classes = badge.get('class', [])
                    badge_text = badge.get_text(strip=True).lower()
                    
                    if any('bg-green' in str(cls) for cls in badge_classes) or '正常' in badge_text or 'normal' in badge_text:
                        account_info['status'] = 'normal'
                        break
                    elif any('bg-red' in str(cls) for cls in badge_classes) or '异常' in badge_text or 'abnormal' in badge_text:
                        account_info['status'] = 'abnormal'
                        break
                
                # 提取最后检查时间
                subtitle_elements = card.find_all('p', class_='card-subtitle')
                for subtitle in subtitle_elements:
                    text = subtitle.get_text(strip=True)
                    if '最后检查' in text or 'lastCheck' in text or '检查时间' in text:
                        # 提取冒号后的内容
                        if ':' in text:
                            account_info['last_check'] = text.split(':', 1)[-1].strip()
                        else:
                            account_info['last_check'] = text
                
                # 提取备注信息
                for subtitle in subtitle_elements:
                    text = subtitle.get_text(strip=True)
                    if '备注' in text or 'remark' in text or '说明' in text:
                        if ':' in text:
                            remark = text.split(':', 1)[-1].strip()
                            if remark:
                                account_info['frontend_remark'] = remark
                
                # 提取密码 - 多种方式尝试
                password_found = False
                
                # 方法1：通过按钮的data-clipboard-text属性
                buttons = card.find_all('button', {'data-clipboard-text': True})
                for button in buttons:
                    button_id = button.get('id', '')
                    button_text = button.get_text(strip=True).lower()
                    
                    if 'password' in button_id or '密码' in button_text or 'pass' in button_text:
                        password = button.get('data-clipboard-text')
                        if password and password != account_info.get('username', ''):
                            account_info['password'] = password
                            password_found = True
                            break
                
                # 方法2：如果没找到密码，尝试查找其他密码标识
                if not password_found:
                    # 查找可能包含密码的元素
                    password_elements = [
                        card.find('input', {'name': 'password'}),
                        card.find('input', {'type': 'password'}),
                        card.find(text=lambda text: text and ('密码' in text or 'password' in text.lower()))
                    ]
                    
                    for elem in password_elements:
                        if elem and hasattr(elem, 'get'):
                            password = elem.get('value') or elem.get('data-value')
                            if password:
                                account_info['password'] = password
                                break
                
                # 方法3：如果仍然没有密码，尝试从按钮的data-clipboard-text中获取第二个按钮的内容
                if not password_found and len(buttons) >= 2:
                    second_button = buttons[1]
                    password = second_button.get('data-clipboard-text')
                    if password and password != account_info.get('username', ''):
                        account_info['password'] = password
                
                # 只有当找到用户名时才添加账号
                if 'username' in account_info and account_info['username']:
                    accounts.append(account_info)
            
            logger.info(f"从 {self.share_url} 提取到 {len(accounts)} 个账号")
            return accounts
            
        except Exception as e:
            logger.error(f"提取账号信息失败: {e}")
            return []

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.mysql_config = mysql_config
        
    def get_connection(self):
        """获取MySQL连接"""
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            return connection
        except Error as e:
            logger.error(f"MySQL连接失败: {e}")
            return None
    
    def init_database(self):
        """初始化数据库表"""
        connection = self.get_connection()
        if not connection:
            return False
            
        try:
            cursor = connection.cursor()
            
            # 创建UUID访问控制表
            create_share_table = """
            CREATE TABLE IF NOT EXISTS share_access (
                id INT AUTO_INCREMENT PRIMARY KEY,
                uuid VARCHAR(36) UNIQUE NOT NULL,
                is_activated BOOLEAN DEFAULT FALSE,
                activated_at TIMESTAMP NULL,
                expires_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_uuid (uuid),
                INDEX idx_activated (is_activated),
                INDEX idx_expires (expires_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
            """
            
            # 创建分享链接配置表
            create_config_table = """
            CREATE TABLE IF NOT EXISTS share_config (
                id INT AUTO_INCREMENT PRIMARY KEY,
                share_url VARCHAR(500) NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
            """
            
            cursor.execute(create_share_table)
            cursor.execute(create_config_table)
            connection.commit()
            
            logger.info("数据库表初始化成功")
            return True
            
        except Error as e:
            logger.error(f"初始化数据库失败: {e}")
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def create_share_uuid(self):
        """创建新的分享UUID"""
        connection = self.get_connection()
        if not connection:
            return None
            
        try:
            cursor = connection.cursor()
            share_uuid = str(uuid.uuid4())
            
            query = "INSERT INTO share_access (uuid) VALUES (%s)"
            cursor.execute(query, (share_uuid,))
            connection.commit()
            
            logger.info(f"创建分享UUID: {share_uuid}")
            return share_uuid
            
        except Error as e:
            logger.error(f"创建分享UUID失败: {e}")
            return None
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def get_share_status(self, share_uuid):
        """获取分享链接状态"""
        connection = self.get_connection()
        if not connection:
            return None
            
        try:
            cursor = connection.cursor(dictionary=True)
            
            query = "SELECT * FROM share_access WHERE uuid = %s"
            cursor.execute(query, (share_uuid,))
            result = cursor.fetchone()
            
            return result
            
        except Error as e:
            logger.error(f"获取分享状态失败: {e}")
            return None
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def activate_share(self, share_uuid):
        """激活分享链接"""
        connection = self.get_connection()
        if not connection:
            return False
            
        try:
            cursor = connection.cursor()
            
            # 使用本地时区时间
            activated_at = get_local_now().replace(tzinfo=None)  # 数据库存储时移除时区信息
            expires_at = activated_at + datetime.timedelta(hours=config_class.SHARE_EXPIRE_HOURS)
            
            query = """
            UPDATE share_access 
            SET is_activated = TRUE, activated_at = %s, expires_at = %s 
            WHERE uuid = %s AND is_activated = FALSE
            """
            cursor.execute(query, (activated_at, expires_at, share_uuid))
            connection.commit()
            
            if cursor.rowcount > 0:
                logger.info(f"激活分享链接: {share_uuid}")
                return True
            else:
                logger.warning(f"分享链接已激活或不存在: {share_uuid}")
                return False
            
        except Error as e:
            logger.error(f"激活分享链接失败: {e}")
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    def set_assigned_accounts(self, share_uuid, account_ids):
        """为分享链接设置分配的账号ID列表"""
        connection = self.get_connection()
        if not connection:
            return False
            
        try:
            cursor = connection.cursor()
            
            # 将账号ID列表转换为JSON字符串存储
            assigned_accounts_json = json.dumps(account_ids, ensure_ascii=False)
            
            query = """
            UPDATE share_access 
            SET assigned_accounts = %s 
            WHERE uuid = %s
            """
            cursor.execute(query, (assigned_accounts_json, share_uuid))
            connection.commit()
            
            if cursor.rowcount > 0:
                logger.info(f"设置分享链接 {share_uuid} 的分配账号: {len(account_ids)} 个")
                return True
            else:
                logger.warning(f"分享链接不存在: {share_uuid}")
                return False
            
        except Error as e:
            logger.error(f"设置分配账号失败: {e}")
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    def get_assigned_accounts(self, share_uuid):
        """获取分享链接已分配的账号ID列表"""
        connection = self.get_connection()
        if not connection:
            return None
            
        try:
            cursor = connection.cursor(dictionary=True)
            
            query = "SELECT assigned_accounts FROM share_access WHERE uuid = %s"
            cursor.execute(query, (share_uuid,))
            result = cursor.fetchone()
            
            if result and result['assigned_accounts']:
                try:
                    return json.loads(result['assigned_accounts'])
                except json.JSONDecodeError:
                    logger.error(f"解析分配账号JSON失败: {result['assigned_accounts']}")
                    return None
            return None
            
        except Error as e:
            logger.error(f"获取分配账号失败: {e}")
            return None
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    def activate_share_with_accounts(self, share_uuid, account_ids):
        """激活分享链接并设置分配的账号（原子操作）"""
        connection = self.get_connection()
        if not connection:
            return False
            
        try:
            cursor = connection.cursor()
            
            # 使用本地时区时间
            activated_at = get_local_now().replace(tzinfo=None)  # 数据库存储时移除时区信息
            expires_at = activated_at + datetime.timedelta(hours=config_class.SHARE_EXPIRE_HOURS)
            assigned_accounts_json = json.dumps(account_ids, ensure_ascii=False)
            
            query = """
            UPDATE share_access 
            SET is_activated = TRUE, activated_at = %s, expires_at = %s, assigned_accounts = %s
            WHERE uuid = %s AND is_activated = FALSE
            """
            cursor.execute(query, (activated_at, expires_at, assigned_accounts_json, share_uuid))
            connection.commit()
            
            if cursor.rowcount > 0:
                logger.info(f"激活分享链接并分配账号: {share_uuid}, {len(account_ids)} 个账号")
                return True
            else:
                logger.warning(f"分享链接已激活或不存在: {share_uuid}")
                return False
            
        except Error as e:
            logger.error(f"激活分享链接并分配账号失败: {e}")
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def get_active_share_urls(self):
        """获取活跃的分享链接"""
        connection = self.get_connection()
        if not connection:
            return []
            
        try:
            cursor = connection.cursor()
            
            query = "SELECT share_url FROM share_config WHERE is_active = TRUE"
            cursor.execute(query)
            results = cursor.fetchall()
            
            return [row[0] for row in results]
            
        except Error as e:
            logger.error(f"获取分享链接失败: {e}")
            return []
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

class RedisManager:
    """Redis管理器"""
    
    def __init__(self):
        self.redis_client = redis_client
        self.account_key = "accounts_pool"  # 账号池，永久存储
        self.share_key_prefix = "share:"    # 分享访问控制
    
    def store_accounts(self, accounts):
        """存储账号信息到Redis（永久存储，作为数据池）"""
        try:
            # 为每个账号添加唯一ID（如果没有的话）
            for i, account in enumerate(accounts):
                if 'id' not in account:
                    # 使用用户名和索引生成简单的ID
                    account['id'] = f"{account.get('username', 'unknown')}_{i}"
            
            # 直接覆盖存储账号池
            account_data = {
                'accounts': accounts,
                'updated_at': get_local_now().isoformat(),  # 使用本地时区时间
                'count': len(accounts)
            }
            
            self.redis_client.set(
                self.account_key,
                json.dumps(account_data, ensure_ascii=False)
            )
            
            logger.info(f"更新账号池: {len(accounts)} 个账号")
            return True
            
        except Exception as e:
            logger.error(f"存储账号到Redis失败: {e}")
            return False

    def get_accounts(self):
        """从Redis获取账号信息"""
        try:
            data = self.redis_client.get(self.account_key)
            if data:
                account_data = json.loads(data)
                return account_data.get('accounts', [])
            return []
            
        except Exception as e:
            logger.error(f"从Redis获取账号失败: {e}")
            return []

    def get_accounts_by_ids(self, account_ids):
        """根据账号ID列表获取特定账号信息"""
        try:
            all_accounts = self.get_accounts()
            if not all_accounts:
                return []
            
            # 创建ID到账号的映射
            account_map = {acc.get('id'): acc for acc in all_accounts if acc.get('id')}
            
            # 根据ID列表获取账号，保持顺序
            selected_accounts = []
            for acc_id in account_ids:
                if acc_id in account_map:
                    selected_accounts.append(account_map[acc_id])
            
            return selected_accounts
            
        except Exception as e:
            logger.error(f"根据ID获取账号失败: {e}")
            return []

    def assign_accounts_to_share(self, share_uuid, count=None):
        """为分享链接分配固定的账号"""
        try:
            if count is None:
                count = config_class.ACCOUNTS_PER_SHARE
            
            all_accounts = self.get_accounts()
            if not all_accounts:
                logger.warning("账号池为空，无法分配账号")
                return None
            
            if len(all_accounts) < count:
                logger.warning(f"账号池不足，需要{count}个，只有{len(all_accounts)}个")
                count = len(all_accounts)
            
            # 使用分享UUID作为种子，确保每次获取相同的账号
            import hashlib
            import random
            
            seed = int(hashlib.md5(share_uuid.encode()).hexdigest()[:8], 16)
            random.seed(seed)
            
            # 选择账号
            selected_accounts = random.sample(all_accounts, count)
            account_ids = [acc.get('id') for acc in selected_accounts if acc.get('id')]
            
            logger.info(f"为分享链接 {share_uuid} 分配了 {len(account_ids)} 个账号")
            return account_ids
            
        except Exception as e:
            logger.error(f"分配账号失败: {e}")
            return None
    
    def get_account_pool_info(self):
        """获取账号池信息"""
        try:
            data = self.redis_client.get(self.account_key)
            if data:
                return json.loads(data)
            return {'accounts': [], 'count': 0, 'updated_at': None}
            
        except Exception as e:
            logger.error(f"获取账号池信息失败: {e}")
            return {'accounts': [], 'count': 0, 'updated_at': None}
    
    def set_share_active(self, share_uuid):
        """设置分享链接为激活状态（根据配置的过期时间）"""
        try:
            key = f"{self.share_key_prefix}{share_uuid}"
            share_data = {
                'activated_at': get_local_now().isoformat(),  # 使用本地时区时间
                'status': 'active'
            }
            
            # 设置过期时间（小时转换为秒）
            expire_seconds = config_class.SHARE_EXPIRE_HOURS * 3600
            self.redis_client.setex(
                key,
                expire_seconds,
                json.dumps(share_data, ensure_ascii=False)
            )
            
            logger.info(f"设置分享链接激活: {share_uuid}, 过期时间: {config_class.SHARE_EXPIRE_HOURS}小时")
            return True
            
        except Exception as e:
            logger.error(f"设置分享链接激活失败: {e}")
            return False
    
    def is_share_active(self, share_uuid):
        """检查分享链接是否在Redis中激活"""
        try:
            key = f"{self.share_key_prefix}{share_uuid}"
            data = self.redis_client.get(key)
            return data is not None
            
        except Exception as e:
            logger.error(f"检查分享链接状态失败: {e}")
            return False
    
    def get_share_info(self, share_uuid):
        """获取分享链接信息"""
        try:
            key = f"{self.share_key_prefix}{share_uuid}"
            data = self.redis_client.get(key)
            if data:
                return json.loads(data)
            return None
            
        except Exception as e:
            logger.error(f"获取分享链接信息失败: {e}")
            return None

# 初始化管理器
db_manager = DatabaseManager()
redis_manager = RedisManager()

def update_accounts_task():
    """定时更新账号信息任务"""
    logger.info("开始执行账号信息更新任务")
    
    share_urls = db_manager.get_active_share_urls()
    if not share_urls:
        logger.warning("没有找到活跃的分享链接")
        return
    
    all_accounts = []
    
    for share_url in share_urls:
        extractor = AccountExtractor(share_url)
        accounts = extractor.extract_accounts()
        all_accounts.extend(accounts)
    
    if all_accounts:
        redis_manager.store_accounts(all_accounts)
        logger.info(f"成功更新账号池: {len(all_accounts)} 个账号")
    else:
        logger.warning("没有提取到任何账号信息")

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 如果不需要鉴权，直接通过
        if not config_class.REQUIRE_ADMIN_AUTH:
            return f(*args, **kwargs)
        
        # 检查会话是否存在且有效
        if 'admin_logged_in' not in session:
            return redirect(url_for('admin_login', next=request.url))
        
        # 检查会话是否过期
        if 'admin_login_time' in session:
            login_time = datetime.datetime.fromisoformat(session['admin_login_time'])
            # 转换为本地时区进行比较
            login_time = localize_datetime(login_time)
            now = get_local_now()
            if (now - login_time).total_seconds() > config_class.ADMIN_SESSION_TIMEOUT:
                session.clear()
                flash('会话已过期，请重新登录', 'warning')
                return redirect(url_for('admin_login', next=request.url))
        
        return f(*args, **kwargs)
    return decorated_function

def verify_admin_credentials(username, password):
    """验证管理员凭据"""
    if username == config_class.ADMIN_USERNAME:
        return password == config_class.ADMIN_PASSWORD
    return False

# 路由定义
@app.route('/')
def index():
    """首页"""
    return render_template('index.html')

@app.route(f'/{config_class.ADMIN_PATH}')
@admin_required
def admin():
    """管理页面"""
    return render_template('admin.html')

@app.route(f'/{config_class.ADMIN_PATH}/login', methods=['GET', 'POST'])
def admin_login():
    """管理员登录页面"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if verify_admin_credentials(username, password):
            session['admin_logged_in'] = True
            session['admin_login_time'] = get_local_now().isoformat()  # 使用本地时区时间
            flash('登录成功！', 'success')
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('admin'))
        else:
            flash('登录失败，用户名或密码错误！', 'danger')
            return render_template('login.html')
    return render_template('login.html')

@app.route(f'/{config_class.ADMIN_PATH}/logout')
def admin_logout():
    """管理员退出"""
    session.clear()
    flash('已退出登录', 'info')
    return redirect(url_for('admin_login'))

@app.route('/api/add_share_url', methods=['POST'])
@admin_required
def add_share_url():
    """添加分享链接API"""
    data = request.get_json()
    share_url = data.get('share_url') if data else None
    
    if not share_url:
        return jsonify({'success': False, 'message': '分享链接不能为空'}), 400
    
    # 输入验证和清理
    sanitized_url = sanitize_input(share_url, max_length=500)
    if not sanitized_url:
        return jsonify({'success': False, 'message': '分享链接格式无效或包含非法字符'}), 400
    
    # 基础URL格式验证
    if not (sanitized_url.startswith('http://') or sanitized_url.startswith('https://')):
        return jsonify({'success': False, 'message': '分享链接必须以http://或https://开头'}), 400
    
    connection = db_manager.get_connection()
    if not connection:
        return jsonify({'success': False, 'message': '数据库连接失败'}), 500
    
    try:
        cursor = connection.cursor()
        query = "INSERT INTO share_config (share_url) VALUES (%s)"
        cursor.execute(query, (sanitized_url,))
        connection.commit()
        
        logger.info(f"管理员添加分享链接: {sanitized_url}")
        return jsonify({'success': True, 'message': '添加成功'})
        
    except Error as e:
        logger.error(f"添加分享链接失败: {e}")
        return jsonify({'success': False, 'message': '添加失败'}), 500
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/share/<share_uuid>')
def share_accounts(share_uuid):
    """用户访问分享链接"""
    # 首先验证UUID格式，防止SQL注入
    if not validate_uuid(share_uuid):
        logger.warning(f"无效的UUID格式: {share_uuid}")
        return render_template('error.html', message='分享链接格式错误'), 400
    
    # 检查UUID是否存在于数据库
    share_info = db_manager.get_share_status(share_uuid)
    
    if not share_info:
        return render_template('error.html', message='分享链接不存在'), 404
    
    # 检查是否已过期（数据库中的过期时间）
    if share_info['expires_at'] and get_local_now().replace(tzinfo=None) > share_info['expires_at']:
        return render_template('error.html', message='分享链接已过期'), 410
    
    # 如果已激活，检查Redis中是否还有效
    if share_info['is_activated']:
        if redis_manager.is_share_active(share_uuid):
            # Redis中仍有效，获取该链接分配的固定账号
            assigned_account_ids = db_manager.get_assigned_accounts(share_uuid)
            
            if assigned_account_ids:
                # 根据分配的账号ID获取账号信息
                accounts = redis_manager.get_accounts_by_ids(assigned_account_ids)
                
                # 如果分配的账号不足（可能有账号被删除），重新分配
                if len(accounts) < config_class.ACCOUNTS_PER_SHARE:
                    logger.warning(f"分享链接 {share_uuid} 的分配账号不足，重新分配")
                    new_account_ids = redis_manager.assign_accounts_to_share(share_uuid)
                    if new_account_ids:
                        db_manager.set_assigned_accounts(share_uuid, new_account_ids)
                        accounts = redis_manager.get_accounts_by_ids(new_account_ids)
            else:
                # 没有分配账号记录，重新分配
                logger.info(f"分享链接 {share_uuid} 没有分配记录，重新分配账号")
                account_ids = redis_manager.assign_accounts_to_share(share_uuid)
                if account_ids:
                    db_manager.set_assigned_accounts(share_uuid, account_ids)
                    accounts = redis_manager.get_accounts_by_ids(account_ids)
                else:
                    accounts = []
            
            if not accounts:
                return render_template('error.html', message='暂无可用账号'), 503
            
            share_data = redis_manager.get_share_info(share_uuid)
            return render_template('accounts.html', 
                                 accounts=accounts,
                                 activated_at=share_data['activated_at'])
        else:
            # Redis中已过期
            return render_template('error.html', message='分享链接已过期'), 410
    
    # 首次访问，激活分享链接并分配账号
    all_accounts = redis_manager.get_accounts()
    if not all_accounts:
        return render_template('error.html', message='暂无可用账号'), 503
    
    # 为该链接分配固定的账号
    account_ids = redis_manager.assign_accounts_to_share(share_uuid)
    if not account_ids:
        return render_template('error.html', message='账号分配失败'), 500
    
    # 在数据库中标记为已激活并保存分配的账号
    if db_manager.activate_share_with_accounts(share_uuid, account_ids):
        # 在Redis中设置激活状态
        redis_manager.set_share_active(share_uuid)
        
        # 获取分配的账号信息
        accounts = redis_manager.get_accounts_by_ids(account_ids)
        
        return render_template('accounts.html', 
                             accounts=accounts,
                             activated_at=get_local_now().isoformat())  # 使用本地时区时间
    else:
        return render_template('error.html', message='激活失败，可能已被使用'), 400

@app.route('/api/accounts')
def api_accounts():
    """获取账号信息API"""
    pool_info = redis_manager.get_account_pool_info()
    return jsonify({
        'success': True,
        'count': pool_info['count'],
        'accounts': pool_info['accounts'],
        'updated_at': pool_info['updated_at']
    })

@app.route('/api/trigger_update', methods=['POST'])
@admin_required
def trigger_update():
    """手动触发更新"""
    try:
        update_accounts_task()
        return jsonify({'success': True, 'message': '更新完成'})
    except Exception as e:
        logger.error(f"手动更新失败: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/stats')
@admin_required
def api_stats():
    """获取系统统计信息"""
    try:
        connection = db_manager.get_connection()
        if not connection:
            return jsonify({'success': False, 'message': '数据库连接失败'}), 500
        
        cursor = connection.cursor()
        
        # 统计总分享链接数
        cursor.execute("SELECT COUNT(*) FROM share_access")
        total_shares = cursor.fetchone()[0]
        
        # 统计已激活的分享链接数
        cursor.execute("SELECT COUNT(*) FROM share_access WHERE is_activated = TRUE")
        activated_shares = cursor.fetchone()[0]
        
        # 统计未过期的分享链接数
        cursor.execute("SELECT COUNT(*) FROM share_access WHERE expires_at > NOW()")
        active_shares = cursor.fetchone()[0]
        
        # 获取账号池信息
        pool_info = redis_manager.get_account_pool_info()
        
        return jsonify({
            'success': True,
            'stats': {
                'total_shares': total_shares,
                'activated_shares': activated_shares,
                'active_shares': active_shares,
                'account_count': pool_info['count'],
                'last_update': pool_info['updated_at']
            }
        })
        
    except Error as e:
        logger.error(f"获取统计信息失败: {e}")
        return jsonify({'success': False, 'message': '获取统计失败'}), 500
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/api/recent_shares')
@admin_required
def api_recent_shares():
    """获取最近分享记录"""
    try:
        connection = db_manager.get_connection()
        if not connection:
            return jsonify({'success': False, 'message': '数据库连接失败'}), 500
        
        cursor = connection.cursor(dictionary=True)
        
        # 获取最近50条有效的分享记录（已激活且在有效期内），按激活时间倒序
        query = """
        SELECT 
            uuid,
            is_activated,
            activated_at,
            expires_at,
            created_at,
            '已激活' as status_text
        FROM share_access 
        WHERE is_activated = TRUE 
          AND expires_at > NOW()
        ORDER BY activated_at DESC 
        LIMIT 50
        """
        cursor.execute(query)
        records = cursor.fetchall()
        
        # 格式化时间字段，转换为本地时区显示
        for record in records:
            if record['created_at']:
                record['created_at'] = format_local_time(record['created_at'])
            if record['activated_at']:
                record['activated_at'] = format_local_time(record['activated_at'])
            else:
                record['activated_at'] = '-'
            if record['expires_at']:
                record['expires_at'] = format_local_time(record['expires_at'])
            else:
                record['expires_at'] = '-'
        
        return jsonify({
            'success': True,
            'records': records
        })
        
    except Error as e:
        logger.error(f"获取分享记录失败: {e}")
        return jsonify({'success': False, 'message': '获取分享记录失败'}), 500
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/api/active_shares')
@admin_required
def api_active_shares():
    """获取活跃的分享链接列表"""
    try:
        connection = db_manager.get_connection()
        if not connection:
            return jsonify({'success': False, 'message': '数据库连接失败'}), 500
        
        cursor = connection.cursor(dictionary=True)
        
        # 获取所有分享链接，按创建时间倒序
        query = """
        SELECT uuid, is_activated, activated_at, expires_at, created_at 
        FROM share_access 
        ORDER BY created_at DESC 
        LIMIT 50
        """
        cursor.execute(query)
        shares = cursor.fetchall()
        
        # 格式化时间字段
        for share in shares:
            if share['activated_at']:
                share['activated_at'] = share['activated_at'].isoformat()
            if share['expires_at']:
                share['expires_at'] = share['expires_at'].isoformat()
            if share['created_at']:
                share['created_at'] = share['created_at'].isoformat()
        
        return jsonify({
            'success': True,
            'shares': shares
        })
        
    except Error as e:
        logger.error(f"获取活跃分享链接失败: {e}")
        return jsonify({'success': False, 'message': '获取分享链接失败'}), 500
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/api/revoke_share', methods=['POST'])
@admin_required
def api_revoke_share():
    """撤销分享链接"""
    try:
        data = request.get_json()
        if not data or 'uuid' not in data:
            return jsonify({'success': False, 'message': '缺少UUID参数'}), 400
        
        uuid = data['uuid']
        
        # 验证UUID格式
        if not validate_uuid(uuid):
            return jsonify({'success': False, 'message': 'UUID格式无效'}), 400
        
        connection = db_manager.get_connection()
        if not connection:
            return jsonify({'success': False, 'message': '数据库连接失败'}), 500
        
        cursor = connection.cursor()
        
        # 将过期时间设置为当前时间，使链接立即过期
        current_time = get_local_now().replace(tzinfo=None)
        
        query = """
        UPDATE share_access 
        SET expires_at = %s 
        WHERE uuid = %s AND is_activated = TRUE AND expires_at > NOW()
        """
        cursor.execute(query, (current_time, uuid))
        connection.commit()
        
        if cursor.rowcount > 0:
            # 同时从Redis中删除
            redis_manager.redis_client.delete(f"share:{uuid}")
            
            logger.info(f"管理员撤销分享链接: {uuid}")
            return jsonify({'success': True, 'message': '分享链接已撤销'})
        else:
            return jsonify({'success': False, 'message': '分享链接不存在或已过期'}), 404
        
    except Error as e:
        logger.error(f"撤销分享链接失败: {e}")
        return jsonify({'success': False, 'message': '撤销操作失败'}), 500
    except Exception as e:
        logger.error(f"撤销分享链接异常: {e}")
        return jsonify({'success': False, 'message': '系统错误'}), 500
    finally:
        if 'connection' in locals() and connection and connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/api/share_sources')
@admin_required
def api_share_sources():
    """获取分享源列表"""
    try:
        connection = db_manager.get_connection()
        if not connection:
            return jsonify({'success': False, 'message': '数据库连接失败'}), 500
        
        cursor = connection.cursor(dictionary=True)
        
        query = """
        SELECT id, share_url, is_active, created_at 
        FROM share_config 
        ORDER BY created_at DESC
        """
        cursor.execute(query)
        sources = cursor.fetchall()
        
        # 格式化时间字段
        for source in sources:
            if source['created_at']:
                source['created_at'] = source['created_at'].isoformat()
        
        return jsonify({
            'success': True,
            'sources': sources
        })
        
    except Error as e:
        logger.error(f"获取分享源失败: {e}")
        return jsonify({'success': False, 'message': '获取分享源失败'}), 500
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/api/toggle_source', methods=['POST'])
@admin_required
def api_toggle_source():
    """切换分享源状态"""
    try:
        data = request.get_json()
        if not data or 'id' not in data or 'is_active' not in data:
            return jsonify({'success': False, 'message': '参数不完整'}), 400
        
        source_id = data['id']
        is_active = data['is_active']
        
        connection = db_manager.get_connection()
        if not connection:
            return jsonify({'success': False, 'message': '数据库连接失败'}), 500
        
        cursor = connection.cursor()
        
        query = "UPDATE share_config SET is_active = %s WHERE id = %s"
        cursor.execute(query, (is_active, source_id))
        connection.commit()
        
        if cursor.rowcount > 0:
            action = '启用' if is_active else '禁用'
            logger.info(f"管理员{action}分享源: ID {source_id}")
            return jsonify({'success': True, 'message': f'分享源已{action}'})
        else:
            return jsonify({'success': False, 'message': '分享源不存在'}), 404
        
    except Error as e:
        logger.error(f"切换分享源状态失败: {e}")
        return jsonify({'success': False, 'message': '操作失败'}), 500
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/api/delete_source', methods=['POST'])
@admin_required
def api_delete_source():
    """删除分享源"""
    try:
        data = request.get_json()
        if not data or 'id' not in data:
            return jsonify({'success': False, 'message': '缺少ID参数'}), 400
        
        source_id = data['id']
        
        connection = db_manager.get_connection()
        if not connection:
            return jsonify({'success': False, 'message': '数据库连接失败'}), 500
        
        cursor = connection.cursor()
        
        # 先获取分享源信息用于日志
        cursor.execute("SELECT share_url FROM share_config WHERE id = %s", (source_id,))
        result = cursor.fetchone()
        
        if not result:
            return jsonify({'success': False, 'message': '分享源不存在'}), 404
        
        share_url = result[0]
        
        # 删除分享源
        query = "DELETE FROM share_config WHERE id = %s"
        cursor.execute(query, (source_id,))
        connection.commit()
        
        logger.info(f"管理员删除分享源: {share_url}")
        return jsonify({'success': True, 'message': '分享源已删除'})
        
    except Error as e:
        logger.error(f"删除分享源失败: {e}")
        return jsonify({'success': False, 'message': '删除失败'}), 500
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == '__main__':
    # 初始化数据库
    db_manager.init_database()
    
    # 启动定时任务
    scheduler = BackgroundScheduler()
    scheduler.add_job(
        func=update_accounts_task,
        trigger="interval",
        minutes=config_class.UPDATE_INTERVAL,
        id='update_accounts'
    )
    scheduler.start()
    
    # 首次执行更新任务
    update_accounts_task()
    
    try:
        host = getattr(config_class, 'FLASK_HOST', '0.0.0.0')
        port = getattr(config_class, 'FLASK_PORT', 5000)
        debug = getattr(config_class, 'DEBUG', False)
        
        app.run(debug=debug, host=host, port=port)
    except (KeyboardInterrupt, SystemExit):
        scheduler.shutdown() 