#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账号源URL管理脚本
用于直接向数据库添加或管理账号源URL
"""

import sys
import mysql.connector
from config import get_config

def add_account_source(url):
    """添加账号源URL到数据库"""
    config = get_config()
    
    mysql_config = {
        'host': config.MYSQL_HOST,
        'port': config.MYSQL_PORT,
        'user': config.MYSQL_USER,
        'password': config.MYSQL_PASSWORD,
        'database': config.MYSQL_DATABASE,
        'charset': config.MYSQL_CHARSET
    }
    
    try:
        connection = mysql.connector.connect(**mysql_config)
        cursor = connection.cursor()
        
        # 检查URL是否已存在
        check_query = "SELECT id FROM share_config WHERE share_url = %s"
        cursor.execute(check_query, (url,))
        existing = cursor.fetchone()
        
        if existing:
            print(f"⚠️  URL已存在: {url}")
            return False
        
        # 添加新的URL
        insert_query = "INSERT INTO share_config (share_url, is_active) VALUES (%s, TRUE)"
        cursor.execute(insert_query, (url,))
        connection.commit()
        
        print(f"✅ 成功添加账号源: {url}")
        return True
        
    except Exception as e:
        print(f"❌ 添加失败: {e}")
        return False
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def list_account_sources():
    """列出所有账号源URL"""
    config = get_config()
    
    mysql_config = {
        'host': config.MYSQL_HOST,
        'port': config.MYSQL_PORT,
        'user': config.MYSQL_USER,
        'password': config.MYSQL_PASSWORD,
        'database': config.MYSQL_DATABASE,
        'charset': config.MYSQL_CHARSET
    }
    
    try:
        connection = mysql.connector.connect(**mysql_config)
        cursor = connection.cursor(dictionary=True)
        
        query = "SELECT id, share_url, is_active, created_at FROM share_config ORDER BY created_at DESC"
        cursor.execute(query)
        results = cursor.fetchall()
        
        if not results:
            print("📭 暂无账号源配置")
            return
        
        print("📋 当前账号源列表:")
        print("-" * 80)
        print(f"{'ID':<4} {'状态':<6} {'URL':<50} {'创建时间'}")
        print("-" * 80)
        
        for row in results:
            status = "启用" if row['is_active'] else "禁用"
            url = row['share_url']
            if len(url) > 47:
                url = url[:44] + "..."
            print(f"{row['id']:<4} {status:<6} {url:<50} {row['created_at']}")
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def disable_account_source(source_id):
    """禁用指定的账号源"""
    config = get_config()
    
    mysql_config = {
        'host': config.MYSQL_HOST,
        'port': config.MYSQL_PORT,
        'user': config.MYSQL_USER,
        'password': config.MYSQL_PASSWORD,
        'database': config.MYSQL_DATABASE,
        'charset': config.MYSQL_CHARSET
    }
    
    try:
        connection = mysql.connector.connect(**mysql_config)
        cursor = connection.cursor()
        
        query = "UPDATE share_config SET is_active = FALSE WHERE id = %s"
        cursor.execute(query, (source_id,))
        connection.commit()
        
        if cursor.rowcount > 0:
            print(f"✅ 已禁用账号源 ID: {source_id}")
        else:
            print(f"❌ 未找到账号源 ID: {source_id}")
        
    except Exception as e:
        print(f"❌ 禁用失败: {e}")
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def enable_account_source(source_id):
    """启用指定的账号源"""
    config = get_config()
    
    mysql_config = {
        'host': config.MYSQL_HOST,
        'port': config.MYSQL_PORT,
        'user': config.MYSQL_USER,
        'password': config.MYSQL_PASSWORD,
        'database': config.MYSQL_DATABASE,
        'charset': config.MYSQL_CHARSET
    }
    
    try:
        connection = mysql.connector.connect(**mysql_config)
        cursor = connection.cursor()
        
        query = "UPDATE share_config SET is_active = TRUE WHERE id = %s"
        cursor.execute(query, (source_id,))
        connection.commit()
        
        if cursor.rowcount > 0:
            print(f"✅ 已启用账号源 ID: {source_id}")
        else:
            print(f"❌ 未找到账号源 ID: {source_id}")
        
    except Exception as e:
        print(f"❌ 启用失败: {e}")
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("账号源URL管理脚本")
        print("-" * 40)
        print("使用方法:")
        print("  python add_account_source.py add <URL>        # 添加账号源")
        print("  python add_account_source.py list             # 列出所有账号源")
        print("  python add_account_source.py disable <ID>     # 禁用账号源")
        print("  python add_account_source.py enable <ID>      # 启用账号源")
        print()
        print("示例:")
        print("  python add_account_source.py add https://example.com/accounts")
        print("  python add_account_source.py list")
        print("  python add_account_source.py disable 1")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == 'add':
        if len(sys.argv) < 3:
            print("❌ 请提供要添加的URL")
            sys.exit(1)
        
        url = sys.argv[2]
        if not url.startswith(('http://', 'https://')):
            print("❌ URL必须以http://或https://开头")
            sys.exit(1)
        
        add_account_source(url)
    
    elif command == 'list':
        list_account_sources()
    
    elif command == 'disable':
        if len(sys.argv) < 3:
            print("❌ 请提供要禁用的账号源ID")
            sys.exit(1)
        
        try:
            source_id = int(sys.argv[2])
            disable_account_source(source_id)
        except ValueError:
            print("❌ 账号源ID必须是数字")
            sys.exit(1)
    
    elif command == 'enable':
        if len(sys.argv) < 3:
            print("❌ 请提供要启用的账号源ID")
            sys.exit(1)
        
        try:
            source_id = int(sys.argv[2])
            enable_account_source(source_id)
        except ValueError:
            print("❌ 账号源ID必须是数字")
            sys.exit(1)
    
    else:
        print(f"❌ 未知命令: {command}")
        print("支持的命令: add, list, disable, enable")
        sys.exit(1)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  操作已取消")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        sys.exit(1) 