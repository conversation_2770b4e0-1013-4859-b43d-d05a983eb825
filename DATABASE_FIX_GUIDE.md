# 数据库修复指南

## 问题描述

根据日志分析，系统遇到了两个主要问题：

1. **MySQL排序规则错误**: `Unknown collation: 'utf8mb4_0900_ai_ci'`
2. **Redis连接认证失败**: `Authentication required.`

## 解决方案

### 方案一：使用SQL脚本修复（推荐）

**步骤1：登录MySQL**
```bash
mysql -u idshare -p
# 输入密码：bifT5esQP5ATXHT7
```

**步骤2：执行修复脚本**
```sql
-- 创建兼容的数据库
CREATE DATABASE IF NOT EXISTS `idshare` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 使用数据库
USE `idshare`;

-- 删除现有表（会丢失数据，如有重要数据请先备份）
DROP TABLE IF EXISTS `share_access`;
DROP TABLE IF EXISTS `share_config`;

-- 创建新表
CREATE TABLE `share_access` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `uuid` VARCHAR(36) UNIQUE NOT NULL,
    `is_activated` BOOLEAN DEFAULT FALSE,
    `activated_at` TIMESTAMP NULL,
    `expires_at` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_uuid` (`uuid`),
    INDEX `idx_activated` (`is_activated`),
    INDEX `idx_expires` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `share_config` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `share_url` VARCHAR(500) NOT NULL,
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
```

**步骤3：验证修复**
```sql
SHOW TABLE STATUS;
SELECT 'Database fixed successfully!' AS message;
```

### 方案二：使用命令行执行SQL文件

```bash
mysql -u idshare -p idshare < fix_database.sql
```

### 方案三：修改MySQL配置（如果有管理权限）

如果您有MySQL服务器管理权限，可以修改MySQL配置来支持新的排序规则：

**编辑MySQL配置文件** (通常在 `/etc/mysql/mysql.conf.d/mysqld.cnf`)：
```ini
[mysqld]
character-set-server = utf8mb4
collation-server = utf8mb4_general_ci
```

然后重启MySQL服务：
```bash
sudo systemctl restart mysql
```

## Redis连接问题修复

### 问题分析
Redis连接错误 "Authentication required" 通常是因为：
1. Redis设置了密码但配置文件中没有正确配置
2. 配置文件中的密码为空字符串但Redis需要密码

### 解决步骤

**步骤1：检查Redis配置**
```bash
redis-cli info server
# 如果提示需要密码，说明Redis设置了密码
```

**步骤2：测试Redis连接**
```bash
# 无密码连接测试
redis-cli -p 6379 -n 8 ping

# 有密码连接测试（如果设置了密码）
redis-cli -p 6379 -n 8 -a your_password ping
```

**步骤3：修改.env配置**

根据测试结果修改 `.env` 文件：

```bash
# 如果Redis没有密码
REDIS_PASSWORD=

# 如果Redis有密码
REDIS_PASSWORD=your_actual_redis_password

# 其他Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=8
```

**步骤4：重启应用**
```bash
# 停止当前应用
# 重新启动
python app.py
```

## 完整修复流程

### 1. 备份现有数据（重要！）
```bash
mysqldump -u idshare -p idshare > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. 修复MySQL数据库
执行上述SQL命令或运行SQL脚本

### 3. 修复Redis配置
检查并更新 `.env` 文件中的Redis配置

### 4. 测试系统
```bash
# 测试生成链接
python generate_links.py --count 5

# 测试Web应用
python app.py
```

## 验证修复结果

### MySQL验证
```sql
USE idshare;
SHOW TABLE STATUS;
-- 确认排序规则为 utf8mb4_general_ci
```

### Redis验证
```bash
redis-cli -p 6379 -n 8 ping
# 应该返回 PONG
```

### 应用验证
访问 `http://localhost:5000` 检查是否正常工作

## 常见问题

### Q1: 仍然出现排序规则错误
**A**: 确保删除了所有旧表，并且使用了正确的SQL创建新表

### Q2: Redis连接仍然失败
**A**: 检查Redis服务是否运行，端口是否正确，数据库编号是否存在

### Q3: 数据丢失了怎么办
**A**: 使用备份文件恢复：
```bash
mysql -u idshare -p idshare < backup_20250716_110000.sql
```

## 联系支持

如果仍然无法解决问题，请提供：
1. 完整的错误日志
2. MySQL版本 (`mysql --version`)
3. Redis版本 (`redis-server --version`)
4. 系统信息 (`uname -a`)

这样可以提供更精确的解决方案。 