# 分享链接管理功能说明

## 功能概述

分享链接管理功能提供了完整的分享链接生命周期管理，包括链接状态筛选、批量操作和提前过期等功能。

## 主要功能

### 1. 分享链接状态筛选

支持以下状态筛选：
- **全部**：显示所有分享链接
- **已激活未过期**：显示当前有效的分享链接
- **已过期**：显示已过期的分享链接
- **未激活**：显示尚未被访问的分享链接

### 2. 时间范围筛选

- **全部时间**：不限制时间范围
- **今天**：显示今天创建的链接
- **本周**：显示本周创建的链接
- **本月**：显示本月创建的链接

### 3. 搜索功能

支持按UUID搜索分享链接，方便快速定位特定链接。

### 4. 批量操作

- **批量选择**：支持全选/取消全选
- **批量过期**：可以同时让多个已激活的链接立即过期

### 5. 单个链接操作

- **提前过期**：对于已激活且未过期的链接，可以手动设置为立即过期
- **复制链接**：快速复制分享链接到剪贴板

## 页面访问

管理员登录后，通过以下路径访问：
```
/{ADMIN_PATH}/shares
```

默认路径：`/admin_panel_secure/shares`

## API接口

### 获取分享链接列表
```http
GET /api/shares/list
```

返回所有分享链接的详细信息，包括状态、创建时间、激活时间、过期时间等。

### 单个链接过期
```http
POST /api/shares/expire
Content-Type: application/json

{
  "uuid": "分享链接的UUID"
}
```

### 批量链接过期
```http
POST /api/shares/batch_expire
Content-Type: application/json

{
  "uuids": ["uuid1", "uuid2", "uuid3"]
}
```

## 过期时间逻辑

### 24小时过期机制

1. **激活时间计算**：当用户首次访问分享链接时，系统记录激活时间
2. **过期时间设置**：过期时间 = 激活时间 + 24小时（可通过`SHARE_EXPIRE_HOURS`配置）
3. **双重检查机制**：
   - 数据库存储精确的过期时间
   - Redis设置相同的过期时间，确保数据一致性

### 过期检查逻辑

```python
# 数据库过期检查
if share_info['expires_at'] and get_local_now().replace(tzinfo=None) > share_info['expires_at']:
    return render_template('error.html', message='分享链接已过期'), 410

# Redis过期检查
if not redis_manager.is_share_active(share_uuid):
    return render_template('error.html', message='分享链接已过期'), 410
```

### 时区处理

系统使用本地时区（默认`Asia/Shanghai`）进行时间计算：
- 数据库存储时移除时区信息
- Redis使用本地时区时间
- 前端显示时转换为本地时区

## 安全特性

### 1. UUID格式验证
所有UUID参数都经过严格的格式验证，防止SQL注入。

### 2. 权限控制
所有管理接口都需要管理员权限（`@admin_required`装饰器）。

### 3. 参数验证
- 批量操作限制UUID数量
- 输入参数类型检查
- 数据库连接异常处理

## 配置参数

### 环境变量

```bash
# 分享链接过期时间（小时）
SHARE_EXPIRE_HOURS=24

# 每个分享链接分配的账号数量
ACCOUNTS_PER_SHARE=4

# 系统时区
TIMEZONE=Asia/Shanghai

# 管理员路径（安全考虑）
ADMIN_PATH=admin_panel_secure
```

### 配置文件

在`config.py`中可以修改默认值：

```python
class Config:
    SHARE_EXPIRE_HOURS = int(os.environ.get('SHARE_EXPIRE_HOURS', 24))
    ACCOUNTS_PER_SHARE = int(os.environ.get('ACCOUNTS_PER_SHARE', 4))
    TIMEZONE = os.environ.get('TIMEZONE') or 'Asia/Shanghai'
```

## 故障排除

### 1. 时间同步问题

**现象**：Redis和数据库的过期时间不一致
**解决**：系统已修复此问题，现在Redis过期时间基于数据库的过期时间计算

### 2. 批量操作失败

**现象**：批量过期操作部分成功
**检查**：
- 确认选中的链接是否都是已激活且未过期状态
- 查看应用日志获取详细错误信息

### 3. 页面加载缓慢

**优化**：
- 分页显示（每页20条）
- 数据库索引优化
- 前端异步加载

## 数据库结构

### share_access表结构

```sql
CREATE TABLE share_access (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL,
    is_activated BOOLEAN DEFAULT FALSE,
    activated_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_accounts TEXT DEFAULT NULL,
    INDEX idx_uuid (uuid),
    INDEX idx_activated (is_activated),
    INDEX idx_expires (expires_at),
    INDEX idx_assigned_accounts (assigned_accounts(100))
);
```

### 关键字段说明

- `uuid`：分享链接的唯一标识符
- `is_activated`：是否已被激活（首次访问）
- `activated_at`：激活时间
- `expires_at`：过期时间
- `assigned_accounts`：分配给该链接的账号ID列表（JSON格式）

## 监控和日志

系统会记录以下关键操作：
- 分享链接激活
- 管理员过期操作
- 批量操作结果
- 异常和错误信息

日志级别可通过`LOG_LEVEL`环境变量配置。
