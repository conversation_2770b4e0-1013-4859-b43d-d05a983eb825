{% extends "admin_base.html" %}

{% block title %}管理仪表盘 - ID共享租用系统{% endblock %}
{% block page_title %}管理仪表盘{% endblock %}

{% block main_content %}
<!-- 快速操作栏 -->
<div class="mb-8 flex flex-wrap gap-3">
    <button onclick="refreshStats()" class="btn-flat btn-primary inline-flex items-center">
        <i class="fas fa-sync-alt mr-2"></i>刷新数据
    </button>
    <button onclick="showAddSourceModal()" class="btn-flat inline-flex items-center border-green-300 text-green-600 hover:bg-green-50">
        <i class="fas fa-plus mr-2"></i>添加分享源
    </button>
    <button onclick="triggerUpdate()" class="btn-flat inline-flex items-center border-purple-300 text-purple-600 hover:bg-purple-50">
        <i class="fas fa-cloud-download-alt mr-2"></i>更新账号池
    </button>
</div>

<!-- 统计卡片 -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- 总分享链接 -->
    <div class="admin-card p-6 fade-in">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-link text-blue-600 text-lg"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 mb-1">总分享链接</p>
                <p class="text-2xl font-semibold text-gray-900" id="totalShares">
                    <span class="inline-block w-8 h-6 bg-gray-200 rounded animate-pulse"></span>
                </p>
            </div>
        </div>
    </div>

    <!-- 已激活链接 -->
    <div class="admin-card p-6 fade-in">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-600 text-lg"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 mb-1">已激活链接</p>
                <p class="text-2xl font-semibold text-gray-900" id="activatedShares">
                    <span class="inline-block w-8 h-6 bg-gray-200 rounded animate-pulse"></span>
                </p>
            </div>
        </div>
    </div>

    <!-- 有效链接 -->
    <div class="admin-card p-6 fade-in">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-yellow-600 text-lg"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 mb-1">有效链接</p>
                <p class="text-2xl font-semibold text-gray-900" id="activeShares">
                    <span class="inline-block w-8 h-6 bg-gray-200 rounded animate-pulse"></span>
                </p>
            </div>
        </div>
    </div>

    <!-- 账号池数量 -->
    <div class="admin-card p-6 fade-in">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-purple-600 text-lg"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 mb-1">账号池数量</p>
                <p class="text-2xl font-semibold text-gray-900" id="accountCount">
                    <span class="inline-block w-8 h-6 bg-gray-200 rounded animate-pulse"></span>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- 主要功能区域 -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- 分享链接管理 -->
    <div class="admin-card p-6 fade-in">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-share-alt mr-2 text-blue-600"></i>分享链接管理
            </h3>
            <div class="flex space-x-2">
                <button onclick="loadActiveShares()" class="btn-flat text-xs px-3 py-1 text-blue-600 border-blue-200 hover:bg-blue-50">
                    刷新
                </button>
                <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-md" id="lastUpdateTime">
                    更新中...
                </span>
            </div>
        </div>
        
        <div class="space-y-4">
            <!-- 活跃链接表格 -->
            <div class="overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UUID</th>
                            <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">过期时间</th>
                            <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="activeSharesTable" class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="4" class="px-3 py-8 text-center text-gray-500">
                                <i class="fas fa-spinner fa-spin mr-2"></i>加载中...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 账号源管理 -->
    <div class="bg-white rounded-xl shadow-lg p-6 admin-card fade-in">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-database mr-2 text-green-600"></i>账号源管理
            </h3>
            <div class="flex space-x-2">
                <button onclick="loadShareSources()" class="px-3 py-1 text-sm bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors">
                    刷新
                </button>
                <button onclick="showAddSourceModal()" class="px-3 py-1 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                    <i class="fas fa-plus mr-1"></i>添加
                </button>
            </div>
        </div>
        
        <div class="space-y-4">
            <!-- 分享源列表 -->
            <div class="space-y-2" id="shareSourcesList">
                <div class="flex items-center justify-center py-8 text-gray-500">
                    <i class="fas fa-spinner fa-spin mr-2"></i>加载中...
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加分享源模态框 -->
<div id="addSourceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">添加分享源</h3>
                <button onclick="hideAddSourceModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="addSourceForm" class="space-y-4">
                <div>
                    <label for="shareUrl" class="block text-sm font-medium text-gray-700 mb-2">分享链接URL</label>
                    <input type="url" 
                           id="shareUrl" 
                           name="shareUrl" 
                           required
                           placeholder="https://example.com/share"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" 
                            onclick="hideAddSourceModal()" 
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors">
                        取消
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors">
                        <i class="fas fa-plus mr-2"></i>添加
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 全局变量
let updateInterval;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadStats();
    loadActiveShares();
    loadShareSources();
    // loadSystemLogs(); // 移除系统日志相关调用
    
    // 设置自动刷新
    updateInterval = setInterval(() => {
        loadStats();
        loadActiveShares();
    }, 30000); // 30秒刷新一次
});

// 加载统计信息
async function loadStats() {
    try {
        const response = await fetch('/api/stats');
        const data = await response.json();
        
        if (data.success) {
            updateStatsDisplay(data.stats);
        } else {
            showAlert('获取统计信息失败', 'error');
        }
    } catch (error) {
        console.error('加载统计失败:', error);
        showAlert('网络错误，请稍后重试', 'error');
    }
}

// 更新统计显示
function updateStatsDisplay(stats) {
    document.getElementById('totalShares').textContent = stats.total_shares;
    document.getElementById('activatedShares').textContent = stats.activated_shares;
    document.getElementById('activeShares').textContent = stats.active_shares;
    document.getElementById('accountCount').textContent = stats.account_count;
    
    // 更新最后更新时间
    if (stats.last_update) {
        const updateTime = new Date(stats.last_update).toLocaleString();
        document.getElementById('lastUpdateTime').textContent = `最后更新: ${updateTime}`;
    }
}

// 加载活跃分享链接
async function loadActiveShares() {
    try {
        const response = await fetch('/api/active_shares');
        const data = await response.json();
        
        if (data.success) {
            updateActiveSharesTable(data.shares);
        } else {
            showAlert('获取分享链接失败', 'error');
        }
    } catch (error) {
        console.error('加载分享链接失败:', error);
        document.getElementById('activeSharesTable').innerHTML = `
            <tr><td colspan="4" class="px-3 py-4 text-center text-red-500">加载失败</td></tr>
        `;
    }
}

// 更新活跃分享链接表格
function updateActiveSharesTable(shares) {
    const tbody = document.getElementById('activeSharesTable');
    
    if (!shares || shares.length === 0) {
        tbody.innerHTML = `
            <tr><td colspan="4" class="px-3 py-8 text-center text-gray-500">暂无活跃的分享链接</td></tr>
        `;
        return;
    }
    
    tbody.innerHTML = shares.map(share => `
        <tr class="hover:bg-gray-50">
            <td class="px-3 py-4 whitespace-nowrap">
                <div class="text-sm font-mono text-gray-900">${share.uuid.substring(0, 8)}...</div>
            </td>
            <td class="px-3 py-4 whitespace-nowrap">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${share.is_activated ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                    ${share.is_activated ? '已激活' : '未激活'}
                </span>
            </td>
            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                ${share.expires_at ? new Date(share.expires_at).toLocaleString() : '-'}
            </td>
            <td class="px-3 py-4 whitespace-nowrap text-sm">
                ${share.is_activated ? 
                    `<button onclick="revokeShare('${share.uuid}')" class="text-red-600 hover:text-red-800 font-medium">撤销</button>` : 
                    `<span class="text-gray-400">-</span>`
                }
            </td>
        </tr>
    `).join('');
}

// 撤销分享链接
async function revokeShare(uuid) {
    if (!confirm('确定要撤销此分享链接吗？此操作不可撤销。')) {
        return;
    }
    
    try {
        const response = await fetch('/api/revoke_share', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ uuid: uuid })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('分享链接已撤销', 'success');
            loadActiveShares(); // 重新加载表格
            loadStats(); // 更新统计
        } else {
            showAlert(data.message || '撤销失败', 'error');
        }
    } catch (error) {
        console.error('撤销分享链接失败:', error);
        showAlert('网络错误，请稍后重试', 'error');
    }
}

// 加载分享源列表
async function loadShareSources() {
    try {
        const response = await fetch('/api/share_sources');
        const data = await response.json();
        
        if (data.success) {
            updateShareSourcesList(data.sources);
        } else {
            showAlert('获取分享源失败', 'error');
        }
    } catch (error) {
        console.error('加载分享源失败:', error);
        document.getElementById('shareSourcesList').innerHTML = `
            <div class="text-center text-red-500 py-4">加载失败</div>
        `;
    }
}

// 更新分享源列表
function updateShareSourcesList(sources) {
    const container = document.getElementById('shareSourcesList');
    
    if (!sources || sources.length === 0) {
        container.innerHTML = `
            <div class="text-center text-gray-500 py-8">
                <i class="fas fa-database text-3xl mb-2"></i>
                <p>暂无分享源配置</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = sources.map(source => `
        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex-1">
                <div class="text-sm font-medium text-gray-900 truncate">${source.share_url}</div>
                <div class="text-xs text-gray-500">创建于: ${new Date(source.created_at).toLocaleString()}</div>
            </div>
            <div class="flex items-center space-x-2 ml-4">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${source.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                    ${source.is_active ? '启用' : '禁用'}
                </span>
                <button onclick="toggleSource(${source.id}, ${!source.is_active})" class="text-blue-600 hover:text-blue-800 text-sm">
                    ${source.is_active ? '禁用' : '启用'}
                </button>
                <button onclick="deleteSource(${source.id})" class="text-red-600 hover:text-red-800 text-sm">
                    删除
                </button>
            </div>
        </div>
    `).join('');
}

// 切换分享源状态
async function toggleSource(id, isActive) {
    try {
        const response = await fetch('/api/toggle_source', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: id, is_active: isActive })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert(`分享源已${isActive ? '启用' : '禁用'}`, 'success');
            loadShareSources();
        } else {
            showAlert(data.message || '操作失败', 'error');
        }
    } catch (error) {
        console.error('切换分享源状态失败:', error);
        showAlert('网络错误，请稍后重试', 'error');
    }
}

// 删除分享源
async function deleteSource(id) {
    if (!confirm('确定要删除此分享源吗？此操作不可撤销。')) {
        return;
    }
    
    try {
        const response = await fetch('/api/delete_source', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: id })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('分享源已删除', 'success');
            loadShareSources();
        } else {
            showAlert(data.message || '删除失败', 'error');
        }
    } catch (error) {
        console.error('删除分享源失败:', error);
        showAlert('网络错误，请稍后重试', 'error');
    }
}

// 显示添加分享源模态框
function showAddSourceModal() {
    document.getElementById('addSourceModal').classList.remove('hidden');
}

// 隐藏添加分享源模态框
function hideAddSourceModal() {
    document.getElementById('addSourceModal').classList.add('hidden');
    document.getElementById('addSourceForm').reset();
}

// 处理添加分享源表单提交
document.getElementById('addSourceForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const shareUrl = document.getElementById('shareUrl').value;
    
    try {
        const response = await fetch('/api/add_share_url', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ share_url: shareUrl })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('分享源添加成功', 'success');
            hideAddSourceModal();
            loadShareSources();
        } else {
            showAlert(data.message || '添加失败', 'error');
        }
    } catch (error) {
        console.error('添加分享源失败:', error);
        showAlert('网络错误，请稍后重试', 'error');
    }
});

// 触发账号池更新
async function triggerUpdate() {
    try {
        const response = await fetch('/api/trigger_update', {
            method: 'POST',
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('账号池更新已触发', 'success');
            setTimeout(loadStats, 2000); // 2秒后刷新统计
        } else {
            showAlert(data.message || '更新失败', 'error');
        }
    } catch (error) {
        console.error('触发更新失败:', error);
        showAlert('网络错误，请稍后重试', 'error');
    }
}

// 手动刷新统计
function refreshStats() {
    loadStats();
    loadActiveShares();
    loadShareSources();
    showAlert('数据已刷新', 'info');
}

// 显示提示消息
function showAlert(message, type = 'info') {
    // 使用SweetAlert2显示提示
    const icon = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
    
    Swal.fire({
        icon: icon,
        title: message,
        showConfirmButton: false,
        timer: 3000,
        toast: true,
        position: 'top-end',
        timerProgressBar: true
    });
}

// 页面卸载时清理定时器
window.addEventListener('beforeunload', function() {
    if (updateInterval) {
        clearInterval(updateInterval);
    }
});
</script>
{% endblock %} 