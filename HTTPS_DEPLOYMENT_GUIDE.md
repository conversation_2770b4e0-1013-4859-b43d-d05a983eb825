# HTTPS反向代理部署指南

## 问题描述

在使用nginx反向代理并启用TLS时，Flask应用的cookie无法正确传递，导致管理员登录后一直重定向到登录界面。

## 解决方案

### 1. Flask应用配置更新 ✅

已更新的配置包括：

#### ProxyFix中间件
```python
from werkzeug.middleware.proxy_fix import ProxyFix

app.wsgi_app = ProxyFix(
    app.wsgi_app, 
    x_for=1,      # X-Forwarded-For
    x_proto=1,    # X-Forwarded-Proto  
    x_host=1,     # X-Forwarded-Host
    x_prefix=1    # X-Forwarded-Prefix
)
```

#### HTTPS安全配置
```python
if use_https or behind_proxy:
    app.config.update(
        SESSION_COOKIE_SECURE=True,     # 只在HTTPS下发送cookie
        SESSION_COOKIE_HTTPONLY=True,   # 防止XSS攻击
        SESSION_COOKIE_SAMESITE='Lax',  # CSRF保护
        PREFERRED_URL_SCHEME='https'    # 强制使用HTTPS URL
    )
```

### 2. 环境变量配置

创建 `.env` 文件并设置以下变量：

```bash
# 生产环境
FLASK_ENV=production

# HTTPS配置
USE_HTTPS=True
BEHIND_PROXY=True
BASE_URL=https://your-domain.com

# 安全配置
SECRET_KEY=your-very-secure-secret-key
ADMIN_PASSWORD=your-secure-password
```

### 3. Nginx配置

使用提供的 `nginx.conf.example` 配置文件，关键配置项：

```nginx
location / {
    proxy_pass http://127.0.0.1:5000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;
}
```

## 部署步骤

### 1. 更新应用代码
```bash
# 拉取最新代码
git pull origin main

# 安装依赖（如果有新的）
pip install -r requirements.txt
```

### 2. 配置环境变量
```bash
# 复制配置模板
cp .env.production.example .env

# 编辑配置文件
nano .env
```

### 3. 配置Nginx
```bash
# 复制nginx配置
sudo cp nginx.conf.example /etc/nginx/sites-available/idshare

# 修改配置文件
sudo nano /etc/nginx/sites-available/idshare

# 启用站点
sudo ln -s /etc/nginx/sites-available/idshare /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重新加载nginx
sudo systemctl reload nginx
```

### 4. 重启Flask应用
```bash
# 如果使用systemd服务
sudo systemctl restart idshare

# 或者直接运行
python app.py
```

## 故障排除

### 1. Cookie问题诊断

#### 检查浏览器开发者工具
1. 打开浏览器开发者工具 (F12)
2. 转到 Network 标签
3. 尝试登录
4. 检查请求头中的 Cookie 和响应头中的 Set-Cookie

#### 正确的Cookie应该包含：
```
Set-Cookie: session=...; HttpOnly; Path=/; SameSite=Lax; Secure
```

### 2. 常见问题和解决方案

#### 问题1：Cookie没有Secure标志
**现象**：在HTTPS环境下cookie无法设置
**解决**：确保设置了 `USE_HTTPS=True` 或 `FLASK_ENV=production`

#### 问题2：X-Forwarded-Proto头缺失
**现象**：Flask认为请求是HTTP而不是HTTPS
**解决**：检查nginx配置中的 `proxy_set_header X-Forwarded-Proto $scheme;`

#### 问题3：会话一直过期
**现象**：登录后立即要求重新登录
**解决**：检查服务器时间和时区配置

### 3. 调试命令

#### 检查nginx配置
```bash
sudo nginx -t
sudo nginx -T | grep -A 10 -B 10 "proxy_set_header"
```

#### 检查Flask应用日志
```bash
tail -f /var/log/idshare/app.log
```

#### 检查nginx日志
```bash
tail -f /var/log/nginx/idshare_error.log
tail -f /var/log/nginx/idshare_access.log
```

#### 测试HTTPS连接
```bash
curl -I https://your-domain.com
curl -v https://your-domain.com/admin_panel_secure/login
```

### 4. 验证配置

#### 检查环境变量
```bash
python -c "
from config import get_config
config = get_config()
print(f'FLASK_ENV: {config.FLASK_ENV}')
print(f'USE_HTTPS: {getattr(config, \"USE_HTTPS\", False)}')
print(f'BEHIND_PROXY: {getattr(config, \"BEHIND_PROXY\", False)}')
print(f'BASE_URL: {config.BASE_URL}')
"
```

#### 检查Flask配置
```bash
python -c "
from app import app
print('Flask配置:')
for key in ['SESSION_COOKIE_SECURE', 'SESSION_COOKIE_HTTPONLY', 'SESSION_COOKIE_SAMESITE']:
    print(f'{key}: {app.config.get(key)}')
"
```

## 安全建议

### 1. SSL/TLS配置
- 使用强加密套件
- 启用HSTS (HTTP Strict Transport Security)
- 定期更新SSL证书

### 2. 应用安全
- 使用强密码作为SECRET_KEY
- 定期更换管理员密码
- 启用会话超时

### 3. 服务器安全
- 定期更新系统和软件包
- 配置防火墙
- 监控访问日志

## 性能优化

### 1. Nginx优化
```nginx
# 启用gzip压缩
gzip on;
gzip_types text/plain text/css application/json application/javascript;

# 设置缓存
location /static/ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 2. Flask优化
- 使用生产级WSGI服务器（如Gunicorn）
- 配置适当的worker数量
- 启用应用缓存

## 监控和维护

### 1. 日志监控
- 定期检查应用日志
- 监控错误率和响应时间
- 设置日志轮转

### 2. 健康检查
```bash
# 创建健康检查脚本
#!/bin/bash
response=$(curl -s -o /dev/null -w "%{http_code}" https://your-domain.com)
if [ $response -eq 200 ]; then
    echo "应用正常运行"
else
    echo "应用异常，HTTP状态码: $response"
fi
```

通过以上配置和步骤，应该能够解决HTTPS反向代理环境下的cookie传递问题。
