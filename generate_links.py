#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse
import sys
import os
import mysql.connector
from mysql.connector import Error
import uuid
import datetime
from config import get_config

class LinkGenerator:
    """分享链接批量生成器"""
    
    def __init__(self, config_name='development'):
        """初始化生成器"""
        # 设置环境变量，强制使用指定环境
        os.environ['FLASK_ENV'] = config_name
        self.config = get_config()
        
        self.mysql_config = {
            'host': self.config.MYSQL_HOST,
            'port': self.config.MYSQL_PORT,
            'user': self.config.MYSQL_USER,
            'password': self.config.MYSQL_PASSWORD,
            'database': self.config.MYSQL_DATABASE,
            'charset': self.config.MYSQL_CHARSET
        }
        
    def get_connection(self):
        """获取MySQL连接"""
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            return connection
        except Error as e:
            print(f"MySQL连接失败: {e}")
            return None
    
    def create_share_uuid(self):
        """创建新的分享UUID"""
        connection = self.get_connection()
        if not connection:
            return None
            
        try:
            cursor = connection.cursor()
            share_uuid = str(uuid.uuid4())
            
            query = "INSERT INTO share_access (uuid) VALUES (%s)"
            cursor.execute(query, (share_uuid,))
            connection.commit()
            
            return share_uuid
            
        except Error as e:
            print(f"创建分享UUID失败: {e}")
            return None
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def generate_links(self, count, base_url=None, output_file=None):
        """批量生成分享链接"""
        if base_url is None:
            base_url = self.config.BASE_URL
            
        if output_file is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"share_links_{timestamp}.txt"
        
        # 检查批量生成限制
        if count > self.config.MAX_LINKS_PER_BATCH:
            print(f"警告: 请求生成数量({count})超过配置限制({self.config.MAX_LINKS_PER_BATCH})")
            if input("是否继续？(y/N): ").lower() != 'y':
                return None, None
        
        print(f"开始生成 {count} 个分享链接...")
        print(f"环境: {self.config.FLASK_ENV}")
        print(f"基础URL: {base_url}")
        print(f"有效期: {self.config.SHARE_EXPIRE_HOURS} 小时")
        
        generated_links = []
        failed_count = 0
        
        for i in range(count):
            share_uuid = self.create_share_uuid()
            if share_uuid:
                share_url = f"{base_url}/share/{share_uuid}"
                generated_links.append({
                    'uuid': share_uuid,
                    'url': share_url,
                    'created_at': datetime.datetime.now().isoformat()
                })
                print(f"进度: {i+1}/{count} - 生成UUID: {share_uuid}")
            else:
                failed_count += 1
                print(f"进度: {i+1}/{count} - 生成失败")
        
        # 写入文件
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"# ID共享租用系统 - 分享链接\n")
                f.write(f"# 生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# 环境: {self.config.FLASK_ENV}\n")
                f.write(f"# 总数量: {len(generated_links)}\n")
                f.write(f"# 失败数量: {failed_count}\n")
                f.write(f"# 基础URL: {base_url}\n")
                f.write(f"# 有效期: {self.config.SHARE_EXPIRE_HOURS} 小时\n")
                f.write("#" + "="*50 + "\n\n")
                
                for link in generated_links:
                    f.write(f"{link['url']}\n")
                
                f.write("\n# 详细信息\n")
                f.write("#" + "-"*30 + "\n")
                for link in generated_links:
                    f.write(f"UUID: {link['uuid']}\n")
                    f.write(f"URL: {link['url']}\n")
                    f.write(f"创建时间: {link['created_at']}\n")
                    f.write("-" * 40 + "\n")
            
            print(f"\n生成完成！")
            print(f"成功生成: {len(generated_links)} 个链接")
            print(f"失败数量: {failed_count} 个")
            print(f"输出文件: {output_file}")
            
            return generated_links, output_file
            
        except Exception as e:
            print(f"写入文件失败: {e}")
            return None, None
    
    def verify_links(self, count=5):
        """验证最近生成的链接"""
        connection = self.get_connection()
        if not connection:
            return False
            
        try:
            cursor = connection.cursor(dictionary=True)
            
            query = """
            SELECT uuid, is_activated, created_at 
            FROM share_access 
            ORDER BY created_at DESC 
            LIMIT %s
            """
            cursor.execute(query, (count,))
            results = cursor.fetchall()
            
            print(f"\n最近 {len(results)} 个链接状态:")
            print("-" * 60)
            print(f"{'UUID':<40} {'状态':<8} {'创建时间'}")
            print("-" * 60)
            for result in results:
                status = "已激活" if result['is_activated'] else "未激活"
                print(f"{result['uuid']:<40} {status:<8} {result['created_at']}")
            
            return True
            
        except Error as e:
            print(f"验证链接失败: {e}")
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def show_config(self):
        """显示当前配置"""
        print("当前配置信息:")
        print("-" * 40)
        print(f"环境: {self.config.FLASK_ENV}")
        print(f"MySQL主机: {self.config.MYSQL_HOST}:{self.config.MYSQL_PORT}")
        print(f"MySQL数据库: {self.config.MYSQL_DATABASE}")
        print(f"Redis主机: {self.config.REDIS_HOST}:{self.config.REDIS_PORT}")
        print(f"基础URL: {self.config.BASE_URL}")
        print(f"分享有效期: {self.config.SHARE_EXPIRE_HOURS} 小时")
        print(f"更新间隔: {self.config.UPDATE_INTERVAL} 分钟")
        print(f"批量生成限制: {self.config.MAX_LINKS_PER_BATCH} 个")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ID共享租用系统 - 批量生成分享链接')
    parser.add_argument('--count', '-c', type=int, default=10, 
                       help='生成链接数量 (默认: 10)')
    parser.add_argument('--base-url', '-u', type=str,
                       help='基础URL (默认: 从配置文件获取)')
    parser.add_argument('--output', '-o', type=str, 
                       help='输出文件名 (默认: share_links_时间戳.txt)')
    parser.add_argument('--env', '-e', type=str, default='development',
                       choices=['development', 'production', 'testing'],
                       help='环境配置 (默认: development)')
    parser.add_argument('--verify', '-v', action='store_true',
                       help='验证最近生成的链接')
    parser.add_argument('--config', action='store_true',
                       help='显示当前配置信息')
    
    args = parser.parse_args()
    
    # 创建生成器
    try:
        generator = LinkGenerator(args.env)
    except ValueError as e:
        print(f"配置错误: {e}")
        sys.exit(1)
    
    # 显示配置
    if args.config:
        generator.show_config()
        return
    
    # 验证链接
    if args.verify:
        generator.verify_links()
        return
    
    if args.count <= 0:
        print("错误: 生成数量必须大于0")
        sys.exit(1)
    
    # 大量生成确认
    max_batch = generator.config.MAX_LINKS_PER_BATCH
    if args.count > max_batch:
        print(f"警告: 请求生成数量({args.count})超过配置限制({max_batch})")
        confirm = input(f"将要生成 {args.count} 个链接，确认继续？(y/N): ")
        if confirm.lower() != 'y':
            print("已取消生成")
            sys.exit(0)
    elif args.count > 1000:
        confirm = input(f"将要生成 {args.count} 个链接，确认继续？(y/N): ")
        if confirm.lower() != 'y':
            print("已取消生成")
            sys.exit(0)
    
    # 生成链接
    links, output_file = generator.generate_links(
        count=args.count,
        base_url=args.base_url,
        output_file=args.output
    )
    
    if links:
        # 验证生成的链接
        print("\n验证生成的链接...")
        generator.verify_links(min(5, len(links)))
        
        print(f"\n文件路径: {os.path.abspath(output_file)}")
        print("生成完成！")
    else:
        print("生成失败！")
        sys.exit(1)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n操作已取消")
        sys.exit(0)
    except Exception as e:
        print(f"\n发生错误: {e}")
        sys.exit(1) 