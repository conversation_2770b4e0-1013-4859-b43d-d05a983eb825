{% extends "share_base.html" %}

{% block title %}访问错误 - ID共享租用系统{% endblock %}

{% block content %}
<div class="min-h-[60vh] flex items-center justify-center">
    <div class="max-w-lg w-full bg-white rounded-xl shadow-lg p-8 text-center fade-in">
        <!-- 错误图标 -->
        <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-exclamation-triangle text-red-600 text-3xl"></i>
        </div>
        
        <!-- 错误标题 -->
        <h2 class="text-2xl md:text-3xl font-bold text-gray-800 mb-4">访问受限</h2>
        
        <!-- 错误信息 -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p class="text-red-800 font-medium mb-2">{{ message }}</p>
            <p class="text-red-600 text-sm">请检查您的分享链接是否正确或是否已过期</p>
        </div>
        
        <!-- 可能的原因 -->
        <div class="text-left bg-gray-50 rounded-lg p-4 mb-6">
            <h4 class="font-semibold text-gray-800 mb-3 text-center">可能的原因：</h4>
            <div class="space-y-2">
                <div class="flex items-start">
                    <i class="fas fa-clock text-yellow-500 mt-1 mr-3"></i>
                    <span class="text-gray-700 text-sm">分享链接已过期（超过24小时）</span>
                </div>
                <div class="flex items-start">
                    <i class="fas fa-link text-blue-500 mt-1 mr-3"></i>
                    <span class="text-gray-700 text-sm">分享链接格式不正确</span>
                </div>
                <div class="flex items-start">
                    <i class="fas fa-ban text-red-500 mt-1 mr-3"></i>
                    <span class="text-gray-700 text-sm">链接已被使用或无效</span>
                </div>
                <div class="flex items-start">
                    <i class="fas fa-server text-purple-500 mt-1 mr-3"></i>
                    <span class="text-gray-700 text-sm">系统暂时维护中</span>
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="space-y-3">
            <a href="/" 
               class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                <i class="fas fa-home mr-2"></i>
                返回首页
            </a>
            
            <a href="https://shuka.icu" 
               target="_blank"
               class="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                <i class="fas fa-shopping-cart mr-2"></i>
                购买新的分享链接
            </a>
        </div>
        
        <!-- 客服联系 -->
        <div class="mt-6 pt-6 border-t border-gray-200">
            <p class="text-gray-600 text-sm mb-2">
                <i class="fas fa-headset mr-2"></i>
                如需帮助，请联系客服
            </p>
            <p class="text-gray-500 text-xs">我们将尽快为您解决问题</p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 错误页面动画效果
document.addEventListener('DOMContentLoaded', function() {
    // 添加震动效果到错误图标
    const errorIcon = document.querySelector('.fa-exclamation-triangle');
    if (errorIcon) {
        setTimeout(() => {
            errorIcon.classList.add('animate-bounce');
        }, 500);
    }
});
</script>
{% endblock %} 