#!/bin/bash

echo "====================================="
echo "数据库快速修复脚本 (Linux)"
echo "====================================="
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 从.env文件读取配置
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
    echo -e "${GREEN}✓${NC} 已加载 .env 配置文件"
else
    echo -e "${RED}✗${NC} .env 文件不存在，使用默认配置"
    # 设置默认值
    MYSQL_USER=${MYSQL_USER:-idshare}
    MYSQL_PASSWORD=${MYSQL_PASSWORD:-bifT5esQP5ATXHT7}
    MYSQL_DATABASE=${MYSQL_DATABASE:-idshare}
    REDIS_PORT=${REDIS_PORT:-6379}
    REDIS_DB=${REDIS_DB:-8}
fi

echo
echo "1. 测试MySQL连接..."
mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "SELECT 'MySQL连接成功' AS status;" 2>/dev/null
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓${NC} MySQL连接成功"
else
    echo -e "${RED}✗${NC} MySQL连接失败，请检查配置"
    echo "用户名: $MYSQL_USER"
    echo "数据库: $MYSQL_DATABASE"
    echo "请检查密码和服务状态"
    exit 1
fi

echo
echo "2. 备份现有数据（如果存在）..."
backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
mysqldump -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" "$MYSQL_DATABASE" > "$backup_file" 2>/dev/null
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓${NC} 数据已备份到: $backup_file"
else
    echo -e "${YELLOW}⚠${NC} 备份失败或数据库不存在，继续执行修复"
fi

echo
echo "3. 执行数据库修复..."
mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" < fix_database.sql 2>/dev/null
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓${NC} 数据库修复成功"
else
    echo -e "${RED}✗${NC} 数据库修复失败"
    echo "请检查 fix_database.sql 文件是否存在"
    exit 1
fi

echo
echo "4. 验证表结构..."
mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" "$MYSQL_DATABASE" -e "SHOW TABLE STATUS;" 2>/dev/null
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓${NC} 表结构验证成功"
else
    echo -e "${RED}✗${NC} 表结构验证失败"
fi

echo
echo "5. 测试Redis连接..."
if command -v redis-cli > /dev/null; then
    if [ -n "$REDIS_PASSWORD" ] && [ "$REDIS_PASSWORD" != "" ]; then
        redis_result=$(redis-cli -p "$REDIS_PORT" -n "$REDIS_DB" -a "$REDIS_PASSWORD" ping 2>/dev/null)
    else
        redis_result=$(redis-cli -p "$REDIS_PORT" -n "$REDIS_DB" ping 2>/dev/null)
    fi
    
    if [ "$redis_result" = "PONG" ]; then
        echo -e "${GREEN}✓${NC} Redis连接成功"
    else
        echo -e "${YELLOW}⚠${NC} Redis连接可能有问题"
        echo "端口: $REDIS_PORT"
        echo "数据库: $REDIS_DB"
        echo "请检查Redis服务状态和密码配置"
    fi
else
    echo -e "${YELLOW}⚠${NC} redis-cli 未找到，跳过Redis测试"
fi

echo
echo "6. 测试应用连接..."
python3 -c "
try:
    from config import get_config
    import mysql.connector
    import redis
    
    config_class = get_config()
    
    # 测试MySQL
    mysql_config = {
        'host': config_class.MYSQL_HOST,
        'port': config_class.MYSQL_PORT,
        'user': config_class.MYSQL_USER,
        'password': config_class.MYSQL_PASSWORD,
        'database': config_class.MYSQL_DATABASE,
        'charset': config_class.MYSQL_CHARSET,
        'collation': getattr(config_class, 'MYSQL_COLLATION', 'utf8mb4_general_ci'),
        'use_unicode': True,
        'autocommit': False
    }
    
    connection = mysql.connector.connect(**mysql_config)
    cursor = connection.cursor()
    cursor.execute('SELECT COUNT(*) FROM share_access')
    count = cursor.fetchone()[0]
    print(f'✓ 应用MySQL连接成功，share_access表记录数: {count}')
    cursor.close()
    connection.close()
    
    # 测试Redis
    redis_config = {
        'host': config_class.REDIS_HOST,
        'port': config_class.REDIS_PORT,
        'db': config_class.REDIS_DB,
        'decode_responses': True
    }
    if config_class.REDIS_PASSWORD:
        redis_config['password'] = config_class.REDIS_PASSWORD
    
    redis_client = redis.Redis(**redis_config)
    redis_client.ping()
    print('✓ 应用Redis连接成功')
    
except Exception as e:
    print(f'✗ 应用连接测试失败: {e}')
    exit(1)
" 2>/dev/null

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓${NC} 应用连接测试通过"
else
    echo -e "${RED}✗${NC} 应用连接测试失败，请检查Python依赖"
fi

echo
echo "====================================="
echo -e "${GREEN}修复完成！${NC}"
echo "====================================="
echo
echo "现在可以测试应用了："
echo "  python3 generate_links.py --count 5"
echo "  python3 app.py"
echo
echo "如果仍有问题，请查看详细日志："
echo "  tail -f app.log"
echo 