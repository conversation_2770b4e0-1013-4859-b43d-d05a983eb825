{% extends "share_base.html" %}

{% block title %}首页 - ID共享租用系统{% endblock %}

{% block content %}
<!-- 欢迎横幅 -->
<div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl text-white p-8 mb-8 fade-in">
    <div class="max-w-4xl mx-auto text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">欢迎使用ID共享租用系统</h2>
        <p class="text-xl opacity-90 mb-6">安全、便捷、专业的Apple ID共享服务</p>
        <div class="flex flex-col sm:flex-row items-center justify-center gap-4">
            <div class="flex items-center text-blue-100">
                <i class="fas fa-shield-alt text-2xl mr-3"></i>
                <span class="font-medium">安全可靠</span>
            </div>
            <div class="flex items-center text-blue-100">
                <i class="fas fa-clock text-2xl mr-3"></i>
                <span class="font-medium">24小时有效</span>
            </div>
            <div class="flex items-center text-blue-100">
                <i class="fas fa-headset text-2xl mr-3"></i>
                <span class="font-medium">专业客服</span>
            </div>
        </div>
    </div>
</div>

<!-- 功能特色 -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="bg-white rounded-xl shadow-lg p-6 text-center card-hover fade-in">
        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-lock text-green-600 text-2xl"></i>
        </div>
        <h3 class="text-xl font-semibold text-gray-800 mb-3">安全保障</h3>
        <p class="text-gray-600">采用最新安全技术，确保账号信息安全传输与存储</p>
    </div>
    
    <div class="bg-white rounded-xl shadow-lg p-6 text-center card-hover fade-in">
        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-rocket text-blue-600 text-2xl"></i>
        </div>
        <h3 class="text-xl font-semibold text-gray-800 mb-3">快速便捷</h3>
        <p class="text-gray-600">一键获取账号信息，无需等待，即刻使用</p>
    </div>
    
    <div class="bg-white rounded-xl shadow-lg p-6 text-center card-hover fade-in">
        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-users text-purple-600 text-2xl"></i>
        </div>
        <h3 class="text-xl font-semibold text-gray-800 mb-3">专业服务</h3>
        <p class="text-gray-600">7×24小时客服支持，随时为您解决问题</p>
    </div>
</div>

<!-- 购买指引 -->
<div class="bg-white rounded-xl shadow-lg border border-gray-100 p-8 mb-8 fade-in">
    <div class="text-center mb-8">
        <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-shopping-cart text-indigo-600 text-2xl"></i>
        </div>
        <h3 class="text-2xl font-semibold text-gray-800 mb-3">获取共享账号</h3>
        <p class="text-gray-600 max-w-2xl mx-auto">
            想要获取高质量的Apple ID共享账号？请前往我们的官方购买平台
        </p>
    </div>
    
    <div class="text-center">
        <a href="https://shuka.icu" 
           target="_blank" 
           class="inline-flex items-center justify-center bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-semibold py-4 px-8 rounded-lg transition duration-200 transform hover:scale-105 shadow-lg">
            <i class="fas fa-external-link-alt mr-3"></i>
            <span class="text-lg">立即购买 - shuka.icu</span>
        </a>
        <p class="text-sm text-gray-500 mt-4">
            <i class="fas fa-info-circle mr-1"></i>
            安全可靠的官方购买渠道
        </p>
    </div>
</div>

<!-- 使用流程 -->
<div class="bg-white rounded-xl shadow-lg border border-gray-100 p-8 fade-in">
    <div class="text-center mb-8">
        <h3 class="text-2xl font-semibold text-gray-800 mb-3">使用流程</h3>
        <p class="text-gray-600">简单三步，轻松获取Apple ID</p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="text-center">
            <div class="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-xl">1</div>
            <h4 class="font-semibold text-gray-800 mb-2">购买链接</h4>
            <p class="text-gray-600 text-sm">前往官方平台购买分享链接</p>
        </div>
        
        <div class="text-center">
            <div class="w-12 h-12 bg-green-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-xl">2</div>
            <h4 class="font-semibold text-gray-800 mb-2">访问链接</h4>
            <p class="text-gray-600 text-sm">点击分享链接即可激活使用</p>
        </div>
        
        <div class="text-center">
            <div class="w-12 h-12 bg-purple-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-xl">3</div>
            <h4 class="font-semibold text-gray-800 mb-2">获取账号</h4>
            <p class="text-gray-600 text-sm">复制账号信息在App Store中使用</p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 添加页面动画效果
document.addEventListener('DOMContentLoaded', function() {
    // 为卡片添加延迟动画
    const cards = document.querySelectorAll('.card-hover');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });
});
</script>
{% endblock %} 