// 筛选分享链接
function filterShares() {
    const statusFilter = document.getElementById('statusFilter').value;
    const timeFilter = document.getElementById('timeFilter').value;
    const searchInput = document.getElementById('searchInput').value.toLowerCase();
    
    filteredShares = allShares.filter(share => {
        // 状态筛选
        if (statusFilter !== 'all') {
            const now = new Date();
            const expiresAt = share.expires_at ? new Date(share.expires_at) : null;
            
            switch (statusFilter) {
                case 'active':
                    if (!share.is_activated || !expiresAt || expiresAt <= now) return false;
                    break;
                case 'expired':
                    if (!share.is_activated || !expiresAt || expiresAt > now) return false;
                    break;
                case 'inactive':
                    if (share.is_activated) return false;
                    break;
            }
        }
        
        // 时间筛选
        if (timeFilter !== 'all') {
            const createdAt = new Date(share.created_at);
            const now = new Date();
            
            switch (timeFilter) {
                case 'today':
                    if (createdAt.toDateString() !== now.toDateString()) return false;
                    break;
                case 'week':
                    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    if (createdAt < weekAgo) return false;
                    break;
                case 'month':
                    const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    if (createdAt < monthAgo) return false;
                    break;
            }
        }
        
        // 搜索筛选
        if (searchInput && !share.uuid.toLowerCase().includes(searchInput)) {
            return false;
        }
        
        return true;
    });
    
    // 重置到第一页
    currentPage = 1;
    selectedUuids.clear();
    
    // 更新显示
    updateSharesTable();
    updateCounts();
    updatePagination();
}

// 更新分享链接表格
function updateSharesTable() {
    const tbody = document.getElementById('sharesTableBody');
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageShares = filteredShares.slice(startIndex, endIndex);
    
    if (pageShares.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="px-6 py-8 text-center text-gray-500">
                    暂无符合条件的分享链接
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = pageShares.map(share => {
        const status = getShareStatus(share);
        const remainingTime = getRemainingTime(share);
        
        return `
            <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                    <input type="checkbox" 
                           class="share-checkbox rounded" 
                           value="${share.uuid}"
                           onchange="updateSelection()"
                           ${selectedUuids.has(share.uuid) ? 'checked' : ''}>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="font-mono text-sm text-gray-900" title="${share.uuid}">
                        ${share.uuid.substring(0, 8)}...
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 text-xs font-medium rounded-full ${status.class}">
                        ${status.text}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${formatDateTime(share.created_at)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${share.activated_at ? formatDateTime(share.activated_at) : '-'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${share.expires_at ? formatDateTime(share.expires_at) : '-'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${remainingTime}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    ${getActionButtons(share)}
                </td>
            </tr>
        `;
    }).join('');
}

// 获取分享链接状态
function getShareStatus(share) {
    if (!share.is_activated) {
        return { text: '未激活', class: 'bg-gray-100 text-gray-800' };
    }
    
    if (!share.expires_at) {
        return { text: '已激活', class: 'bg-blue-100 text-blue-800' };
    }
    
    const now = new Date();
    const expiresAt = new Date(share.expires_at);
    
    if (expiresAt <= now) {
        return { text: '已过期', class: 'bg-red-100 text-red-800' };
    }
    
    return { text: '已激活', class: 'bg-green-100 text-green-800' };
}

// 获取剩余时间
function getRemainingTime(share) {
    if (!share.is_activated || !share.expires_at) {
        return '-';
    }
    
    const now = new Date();
    const expiresAt = new Date(share.expires_at);
    const diff = expiresAt - now;
    
    if (diff <= 0) {
        return '已过期';
    }
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
        return `${hours}小时${minutes}分钟`;
    } else {
        return `${minutes}分钟`;
    }
}

// 获取操作按钮
function getActionButtons(share) {
    const status = getShareStatus(share);
    
    if (status.text === '已激活' && share.expires_at) {
        const now = new Date();
        const expiresAt = new Date(share.expires_at);
        
        if (expiresAt > now) {
            return `
                <button onclick="showSingleExpireModal('${share.uuid}')" 
                        class="text-red-600 hover:text-red-900 mr-2">
                    提前过期
                </button>
                <button onclick="copyShareLink('${share.uuid}')" 
                        class="text-blue-600 hover:text-blue-900">
                    复制链接
                </button>
            `;
        }
    }
    
    return `
        <button onclick="copyShareLink('${share.uuid}')" 
                class="text-blue-600 hover:text-blue-900">
            复制链接
        </button>
    `;
}

// 格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 更新计数显示
function updateCounts() {
    document.getElementById('totalCount').textContent = allShares.length;
    document.getElementById('visibleCount').textContent = filteredShares.length;
}

// 更新分页信息
function updatePagination() {
    const totalPages = Math.ceil(filteredShares.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage + 1;
    const endIndex = Math.min(currentPage * itemsPerPage, filteredShares.length);
    
    document.getElementById('pageStart').textContent = filteredShares.length > 0 ? startIndex : 0;
    document.getElementById('pageEnd').textContent = endIndex;
    document.getElementById('pageTotal').textContent = filteredShares.length;
    document.getElementById('pageInfo').textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
    
    document.getElementById('prevBtn').disabled = currentPage <= 1;
    document.getElementById('nextBtn').disabled = currentPage >= totalPages;
}

// 上一页
function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        updateSharesTable();
        updatePagination();
    }
}

// 下一页
function nextPage() {
    const totalPages = Math.ceil(filteredShares.length / itemsPerPage);
    if (currentPage < totalPages) {
        currentPage++;
        updateSharesTable();
        updatePagination();
    }
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.share-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
        if (selectAll.checked) {
            selectedUuids.add(checkbox.value);
        } else {
            selectedUuids.delete(checkbox.value);
        }
    });
    
    updateSelectionUI();
}

// 更新选择状态
function updateSelection() {
    const checkboxes = document.querySelectorAll('.share-checkbox');
    selectedUuids.clear();
    
    checkboxes.forEach(checkbox => {
        if (checkbox.checked) {
            selectedUuids.add(checkbox.value);
        }
    });
    
    // 更新全选状态
    const selectAll = document.getElementById('selectAll');
    selectAll.checked = checkboxes.length > 0 && selectedUuids.size === checkboxes.length;
    selectAll.indeterminate = selectedUuids.size > 0 && selectedUuids.size < checkboxes.length;
    
    updateSelectionUI();
}

// 更新选择相关UI
function updateSelectionUI() {
    // 这里可以添加选择状态变化时的UI更新逻辑
}

// 显示批量过期模态框
function showBatchExpireModal() {
    if (selectedUuids.size === 0) {
        showAlert('请先选择要过期的分享链接', 'warning');
        return;
    }
    
    // 只显示已激活且未过期的链接数量
    const activeCount = Array.from(selectedUuids).filter(uuid => {
        const share = allShares.find(s => s.uuid === uuid);
        if (!share || !share.is_activated || !share.expires_at) return false;
        
        const now = new Date();
        const expiresAt = new Date(share.expires_at);
        return expiresAt > now;
    }).length;
    
    if (activeCount === 0) {
        showAlert('选中的链接中没有可以过期的（已激活且未过期的）链接', 'warning');
        return;
    }
    
    document.getElementById('selectedCount').textContent = activeCount;
    document.getElementById('batchExpireModal').classList.remove('hidden');
}

// 关闭批量过期模态框
function closeBatchExpireModal() {
    document.getElementById('batchExpireModal').classList.add('hidden');
}

// 确认批量过期
async function confirmBatchExpire() {
    const activeUuids = Array.from(selectedUuids).filter(uuid => {
        const share = allShares.find(s => s.uuid === uuid);
        if (!share || !share.is_activated || !share.expires_at) return false;

        const now = new Date();
        const expiresAt = new Date(share.expires_at);
        return expiresAt > now;
    });

    if (activeUuids.length === 0) {
        showAlert('没有可以过期的链接', 'warning');
        closeBatchExpireModal();
        return;
    }

    try {
        const response = await fetch('/api/shares/batch_expire', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ uuids: activeUuids })
        });

        const data = await response.json();

        if (data.success) {
            showAlert(`成功过期 ${data.expired_count} 个分享链接`, 'success');
            loadShares(); // 重新加载数据
            selectedUuids.clear();
        } else {
            showAlert('批量过期失败: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('批量过期失败:', error);
        showAlert('网络错误，请稍后重试', 'error');
    }

    closeBatchExpireModal();
}

// 显示单个过期模态框
function showSingleExpireModal(uuid) {
    currentExpireUuid = uuid;
    document.getElementById('expireUuid').textContent = uuid;
    document.getElementById('singleExpireModal').classList.remove('hidden');
}

// 关闭单个过期模态框
function closeSingleExpireModal() {
    currentExpireUuid = null;
    document.getElementById('singleExpireModal').classList.add('hidden');
}

// 确认单个过期
async function confirmSingleExpire() {
    if (!currentExpireUuid) return;

    try {
        const response = await fetch('/api/shares/expire', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ uuid: currentExpireUuid })
        });

        const data = await response.json();

        if (data.success) {
            showAlert('分享链接已过期', 'success');
            loadShares(); // 重新加载数据
        } else {
            showAlert('过期失败: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('过期失败:', error);
        showAlert('网络错误，请稍后重试', 'error');
    }

    closeSingleExpireModal();
}

// 复制分享链接
function copyShareLink(uuid) {
    const baseUrl = window.location.origin;
    const shareUrl = `${baseUrl}/share/${uuid}`;

    navigator.clipboard.writeText(shareUrl).then(() => {
        showAlert('分享链接已复制到剪贴板', 'success');
    }).catch(err => {
        console.error('复制失败:', err);
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = shareUrl;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showAlert('分享链接已复制到剪贴板', 'success');
    });
}

// 显示提示消息
function showAlert(message, type = 'info') {
    // 创建提示元素
    const alert = document.createElement('div');
    alert.className = `fixed top-4 right-4 px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-x-full`;

    // 根据类型设置样式
    switch (type) {
        case 'success':
            alert.className += ' bg-green-500 text-white';
            break;
        case 'error':
            alert.className += ' bg-red-500 text-white';
            break;
        case 'warning':
            alert.className += ' bg-yellow-500 text-white';
            break;
        default:
            alert.className += ' bg-blue-500 text-white';
    }

    alert.textContent = message;
    document.body.appendChild(alert);

    // 显示动画
    setTimeout(() => {
        alert.classList.remove('translate-x-full');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        alert.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(alert);
        }, 300);
    }, 3000);
}
