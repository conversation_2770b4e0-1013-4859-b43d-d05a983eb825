# 分享链接批量生成脚本使用指南

本系统提供两个脚本用于批量生成分享链接，取代了前端的手动生成功能。所有配置通过环境变量管理，支持多环境部署。

## 脚本说明

### 1. batch_generate.py（推荐）
简化的批量生成脚本，适合日常使用。

### 2. generate_links.py
完整功能的生成脚本，支持更多参数和验证功能。

## 使用前准备

### 1. 配置环境变量

创建并配置 `.env` 文件：

```bash
# 复制示例配置文件
cp env_example .env

# 编辑配置文件
nano .env
```

### 2. 环境变量配置

在 `.env` 文件中设置以下配置：

```bash
# MySQL 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your_username
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=idshare_db
MYSQL_CHARSET=utf8mb4

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# 系统配置
BASE_URL=http://localhost:5000
SHARE_EXPIRE_HOURS=24
MAX_LINKS_PER_BATCH=10000

# 环境设置
FLASK_ENV=development
```

### 3. 确保依赖安装
```bash
pip install -r requirements.txt
```

## 使用方法

### 简单使用（batch_generate.py）

#### 基本语法
```bash
python batch_generate.py <数量> [文件名]
python batch_generate.py --config    # 显示配置
```

#### 示例

1. **查看当前配置**
```bash
python batch_generate.py --config
```
输出：
```
当前配置信息:
----------------------------------------
环境: development
MySQL: localhost:3306/idshare_db
Redis: localhost:6379/0
基础URL: http://localhost:5000
分享有效期: 24 小时
```

2. **生成100个链接（自动命名文件）**
```bash
python batch_generate.py 100
```
输出文件：`links_20240315_143022.txt`

3. **生成50个链接到指定文件**
```bash
python batch_generate.py 50 vip_links
```
输出文件：`vip_links.txt`

4. **生成大量链接（需要确认）**
```bash
python batch_generate.py 5000
# 系统会提示确认，输入 y 继续
```

### 高级使用（generate_links.py）

#### 基本使用
```bash
python generate_links.py --count 100
```

#### 完整参数
```bash
python generate_links.py \
  --count 100 \
  --base-url https://yourdomain.com \
  --output premium_links.txt \
  --env production
```

#### 查看配置
```bash
python generate_links.py --config
```

#### 验证已生成的链接
```bash
python generate_links.py --verify
```

#### 参数说明
- `--count, -c`: 生成数量（默认10）
- `--base-url, -u`: 基础URL（默认从配置获取）
- `--output, -o`: 输出文件名
- `--env, -e`: 环境配置（development/production/testing）
- `--verify, -v`: 验证最近生成的链接
- `--config`: 显示当前配置

## 环境配置

### 1. 开发环境
默认使用 `.env` 文件中的配置：
```bash
python generate_links.py --count 100
```

### 2. 生产环境
使用生产环境配置：
```bash
# 方法1：通过环境变量
export FLASK_ENV=production
export MYSQL_HOST=prod-mysql-host
export BASE_URL=https://yourdomain.com
python generate_links.py --count 100

# 方法2：通过参数指定
python generate_links.py --count 100 --env production
```

### 3. 测试环境
```bash
python generate_links.py --count 10 --env testing
```

## 输出文件格式

生成的txt文件包含以下内容：

```
# 分享链接批量生成
# 生成时间: 2024-03-15 14:30:22
# 环境: development
# 数量: 100
# 基础URL: http://localhost:5000
# 有效期: 24 小时
#==================================================

http://localhost:5000/share/550e8400-e29b-41d4-a716-************
http://localhost:5000/share/6ba7b810-9dad-11d1-80b4-00c04fd430c8
...

# 详细信息
#------------------------------
UUID: 550e8400-e29b-41d4-a716-************
URL: http://localhost:5000/share/550e8400-e29b-41d4-a716-************
创建时间: 2024-03-15T14:30:22.123456
----------------------------------------
...
```

## 多环境配置管理

### 1. 环境变量优先级
1. 系统环境变量（最高优先级）
2. `.env` 文件
3. 默认配置值

### 2. 生产环境部署

创建生产环境专用的环境变量：

```bash
# 生产环境配置
export FLASK_ENV=production
export SECRET_KEY=your-production-secret-key
export MYSQL_HOST=prod-mysql.example.com
export MYSQL_USER=prod_user
export MYSQL_PASSWORD=secure_password
export MYSQL_DATABASE=idshare_prod
export BASE_URL=https://yourdomain.com
export SHARE_EXPIRE_HOURS=24
export MAX_LINKS_PER_BATCH=50000

# 生成链接
python generate_links.py --count 1000
```

### 3. Docker环境配置

```dockerfile
# Dockerfile
ENV FLASK_ENV=production
ENV MYSQL_HOST=mysql-container
ENV REDIS_HOST=redis-container
ENV BASE_URL=https://yourdomain.com
```

## 安全最佳实践

### 1. 敏感信息保护
```bash
# 确保 .env 文件不被提交到版本控制
echo ".env" >> .gitignore

# 设置文件权限
chmod 600 .env
```

### 2. 生产环境配置验证
```bash
# 验证生产环境配置
python generate_links.py --config --env production
```

### 3. 批量生成限制
系统会根据 `MAX_LINKS_PER_BATCH` 配置限制单次生成数量：

```bash
# 如果超过限制，系统会提示确认
python generate_links.py --count 20000
# 输出：警告: 请求生成数量(20000)超过配置限制(10000)
```

## 自动化脚本

### 1. 定时生成脚本
```bash
#!/bin/bash
# daily_generate.sh

# 设置环境
export FLASK_ENV=production
source /path/to/your/venv/bin/activate

# 生成每日链接
cd /path/to/project
python batch_generate.py 100 daily_$(date +%Y%m%d).txt

# 日志记录
echo "$(date): Generated 100 links" >> /var/log/link_generation.log
```

### 2. Linux定时任务 (crontab)
```bash
# 每天上午9点生成100个链接
0 9 * * * /path/to/daily_generate.sh
```

### 3. Windows任务计划程序
```batch
@echo off
set FLASK_ENV=production
cd /d "C:\path\to\project"
python batch_generate.py 100 daily_%date:~0,4%%date:~5,2%%date:~8,2%.txt
```

## 故障排除

### 1. 配置相关错误

**问题**：配置文件找不到
```
FileNotFoundError: [Errno 2] No such file or directory: '.env'
```
**解决**：
```bash
cp env_example .env
# 编辑 .env 文件设置正确的配置
```

**问题**：生产环境配置验证失败
```
ValueError: 生产环境缺少必需的环境变量: SECRET_KEY, MYSQL_PASSWORD
```
**解决**：
```bash
export SECRET_KEY=your-production-secret-key
export MYSQL_PASSWORD=your-mysql-password
```

### 2. 数据库连接问题

**问题**：MySQL连接失败
```
❌ 生成失败: Access denied for user 'xxx'@'localhost'
```
**解决**：
1. 检查 `.env` 中的数据库配置
2. 验证用户权限
3. 确认数据库服务状态

### 3. 文件权限问题

**问题**：文件写入失败
```
❌ 写入文件失败: Permission denied
```
**解决**：
```bash
# 检查目录权限
ls -la
# 修改权限
chmod 755 .
```

### 4. 批量生成限制

**问题**：超过批量限制
```
警告: 请求生成数量(20000)超过配置限制(10000)
```
**解决**：
1. 修改 `.env` 中的 `MAX_LINKS_PER_BATCH` 值
2. 分批次生成
3. 确认后继续生成

## 监控和维护

### 1. 配置检查命令
```bash
# 检查当前配置
python generate_links.py --config
python batch_generate.py --config

# 验证数据库连接
python -c "from config import get_config; print('配置加载成功')"
```

### 2. 日志监控
```bash
# 查看应用日志
tail -f app.log

# 查看生成脚本输出
python generate_links.py --count 10 --verify
```

### 3. 性能监控
```bash
# 统计生成速度
time python batch_generate.py 1000

# 检查数据库连接数
mysql -e "SHOW PROCESSLIST;"
```

## 升级指南

### 从硬编码配置升级到环境变量

1. **备份现有配置**
```bash
cp config.py config.py.backup
```

2. **创建环境变量文件**
```bash
cp env_example .env
# 将原配置值填入 .env 文件
```

3. **测试新配置**
```bash
python generate_links.py --config
python batch_generate.py --config
```

4. **验证功能**
```bash
python generate_links.py --count 5 --verify
```

## 注意事项

1. **环境变量优先级**：系统环境变量 > .env文件 > 默认值
2. **密码安全**：生产环境避免在命令行中暴露密码
3. **文件权限**：确保 .env 文件权限设置为 600
4. **版本控制**：.env 文件不应提交到Git仓库
5. **备份重要数据**：定期备份生成的链接文件和数据库 