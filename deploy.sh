#!/bin/bash

# ID共享租用系统部署脚本
# 用于自动化部署HTTPS反向代理环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本"
        exit 1
    fi
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查Python版本
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    if [[ $(echo "$python_version >= 3.8" | bc -l) -eq 0 ]]; then
        log_error "需要Python 3.8或更高版本，当前版本: $python_version"
        exit 1
    fi
    
    log_success "Python版本检查通过: $python_version"
    
    # 检查pip
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3未安装"
        exit 1
    fi
    
    # 检查nginx
    if ! command -v nginx &> /dev/null; then
        log_warning "nginx未安装，请手动安装: sudo apt install nginx"
    fi
    
    # 检查MySQL/MariaDB
    if ! command -v mysql &> /dev/null && ! command -v mariadb &> /dev/null; then
        log_warning "MySQL/MariaDB未安装，请手动安装数据库"
    fi
    
    # 检查Redis
    if ! command -v redis-cli &> /dev/null; then
        log_warning "Redis未安装，请手动安装: sudo apt install redis-server"
    fi
}

# 创建虚拟环境
setup_venv() {
    log_info "设置Python虚拟环境..."
    
    if [[ ! -d "venv" ]]; then
        python3 -m venv venv
        log_success "虚拟环境创建成功"
    else
        log_info "虚拟环境已存在"
    fi
    
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    
    log_success "依赖包安装完成"
}

# 配置环境变量
setup_config() {
    log_info "配置环境变量..."
    
    if [[ ! -f ".env" ]]; then
        if [[ -f ".env.production.example" ]]; then
            cp .env.production.example .env
            log_warning "已复制配置模板到 .env，请编辑配置文件"
            log_warning "必须修改以下配置项："
            echo "  - SECRET_KEY"
            echo "  - ADMIN_PASSWORD"
            echo "  - BASE_URL"
            echo "  - 数据库配置"
            echo "  - Redis配置"
            read -p "按回车键继续编辑配置文件..."
            ${EDITOR:-nano} .env
        else
            log_error "配置模板文件不存在"
            exit 1
        fi
    else
        log_info "配置文件已存在"
    fi
}

# 测试配置
test_config() {
    log_info "测试应用配置..."
    
    source venv/bin/activate
    
    if python test_https_config.py; then
        log_success "配置测试通过"
    else
        log_error "配置测试失败，请检查配置"
        exit 1
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    source venv/bin/activate
    
    # 运行数据库初始化
    python -c "
from app import db_manager
try:
    db_manager.init_database()
    print('数据库初始化成功')
except Exception as e:
    print(f'数据库初始化失败: {e}')
    exit(1)
"
    
    log_success "数据库初始化完成"
}

# 配置systemd服务
setup_service() {
    log_info "配置systemd服务..."
    
    if [[ -f "idshare.service.example" ]]; then
        # 替换路径变量
        current_dir=$(pwd)
        sed "s|/var/www/idshare|$current_dir|g" idshare.service.example > idshare.service
        
        log_info "服务文件已生成: idshare.service"
        log_warning "请手动执行以下命令安装服务："
        echo "  sudo cp idshare.service /etc/systemd/system/"
        echo "  sudo systemctl daemon-reload"
        echo "  sudo systemctl enable idshare"
        echo "  sudo systemctl start idshare"
    else
        log_warning "服务模板文件不存在"
    fi
}

# 配置nginx
setup_nginx() {
    log_info "配置nginx..."
    
    if [[ -f "nginx.conf.example" ]]; then
        log_warning "请手动配置nginx："
        echo "  1. 编辑 nginx.conf.example"
        echo "  2. 替换域名和SSL证书路径"
        echo "  3. 复制到nginx配置目录"
        echo "  4. 测试配置: sudo nginx -t"
        echo "  5. 重新加载: sudo systemctl reload nginx"
    else
        log_warning "nginx配置模板文件不存在"
    fi
}

# 创建日志目录
setup_logs() {
    log_info "设置日志目录..."
    
    mkdir -p logs
    touch logs/app.log
    
    log_success "日志目录创建完成"
}

# 主函数
main() {
    echo "=================================="
    echo "ID共享租用系统 - 部署脚本"
    echo "=================================="
    echo
    
    check_root
    check_requirements
    setup_venv
    setup_config
    test_config
    setup_logs
    init_database
    setup_service
    setup_nginx
    
    echo
    log_success "部署脚本执行完成！"
    echo
    echo "下一步："
    echo "1. 配置SSL证书"
    echo "2. 配置nginx反向代理"
    echo "3. 启动systemd服务"
    echo "4. 测试HTTPS访问"
    echo
    echo "有用的命令："
    echo "  启动应用: python app.py"
    echo "  测试配置: python test_https_config.py"
    echo "  查看日志: tail -f logs/app.log"
    echo
}

# 运行主函数
main "$@"
