# Systemd服务文件示例
# 用于ID共享租用系统的生产环境部署

[Unit]
Description=ID Share System - Flask Application
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/var/www/idshare
Environment=PATH=/var/www/idshare/venv/bin
ExecStart=/var/www/idshare/venv/bin/python app.py
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=on-failure
RestartSec=10

# 安全配置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/www/idshare /var/log/idshare
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=idshare

[Install]
WantedBy=multi-user.target

# 安装说明：
# 1. 将此文件复制到 /etc/systemd/system/idshare.service
# 2. 修改路径和用户配置
# 3. 重新加载systemd: sudo systemctl daemon-reload
# 4. 启用服务: sudo systemctl enable idshare
# 5. 启动服务: sudo systemctl start idshare
# 6. 查看状态: sudo systemctl status idshare
# 7. 查看日志: sudo journalctl -u idshare -f
