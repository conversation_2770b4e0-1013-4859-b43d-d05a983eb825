/* 分享链接管理页面样式 */

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 状态标签样式 */
.status-active {
    background-color: #10b981;
    color: white;
}

.status-expired {
    background-color: #ef4444;
    color: white;
}

.status-inactive {
    background-color: #6b7280;
    color: white;
}

/* 表格行悬停效果 */
.table-row:hover {
    background-color: #f9fafb;
    transition: background-color 0.2s ease;
}

/* 操作按钮样式 */
.action-btn {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
}

.action-btn-expire {
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.action-btn-expire:hover {
    background-color: #dc2626;
    color: white;
}

.action-btn-copy {
    background-color: #eff6ff;
    color: #2563eb;
    border: 1px solid #bfdbfe;
}

.action-btn-copy:hover {
    background-color: #2563eb;
    color: white;
}

/* 筛选器样式 */
.filter-container {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
}

/* 分页按钮样式 */
.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 模态框动画 */
.modal-enter {
    animation: modalEnter 0.3s ease-out;
}

@keyframes modalEnter {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 复选框样式 */
.share-checkbox:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

/* 提示消息样式 */
.alert {
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
    border-left: 4px solid;
}

.alert-success {
    background-color: #f0fdf4;
    border-left-color: #22c55e;
    color: #166534;
}

.alert-error {
    background-color: #fef2f2;
    border-left-color: #ef4444;
    color: #991b1b;
}

.alert-warning {
    background-color: #fffbeb;
    border-left-color: #f59e0b;
    color: #92400e;
}

.alert-info {
    background-color: #eff6ff;
    border-left-color: #3b82f6;
    color: #1e40af;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .filter-container {
        flex-direction: column;
        gap: 12px;
    }
    
    .filter-container > div {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip {
    position: relative;
}

.tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1f2937;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
}

/* 统计卡片 */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 4px;
}

.stats-label {
    font-size: 0.875rem;
    opacity: 0.9;
}
