#!/usr/bin/env python3
"""
HTTPS配置测试脚本
用于验证Flask应用的HTTPS和反向代理配置是否正确
"""

import os
import sys
from config import get_config

def test_config():
    """测试配置"""
    print("=== ID共享租用系统 HTTPS配置测试 ===\n")
    
    try:
        config = get_config()
        print(f"✅ 配置加载成功")
        print(f"   环境: {config.FLASK_ENV}")
        print(f"   调试模式: {getattr(config, 'DEBUG', False)}")
        
        # 检查基础配置
        print(f"\n📋 基础配置:")
        print(f"   BASE_URL: {config.BASE_URL}")
        print(f"   SECRET_KEY: {'已设置' if config.SECRET_KEY != 'dev-secret-key-change-this' else '⚠️  使用默认值'}")
        print(f"   ADMIN_PATH: {config.ADMIN_PATH}")
        
        # 检查HTTPS配置
        print(f"\n🔒 HTTPS配置:")
        use_https = getattr(config, 'USE_HTTPS', False)
        behind_proxy = getattr(config, 'BEHIND_PROXY', False)
        print(f"   USE_HTTPS: {use_https}")
        print(f"   BEHIND_PROXY: {behind_proxy}")
        print(f"   FLASK_HOST: {getattr(config, 'FLASK_HOST', '127.0.0.1')}")
        print(f"   FLASK_PORT: {getattr(config, 'FLASK_PORT', 5000)}")
        
        # 检查数据库配置
        print(f"\n🗄️  数据库配置:")
        print(f"   MySQL主机: {config.MYSQL_HOST}:{config.MYSQL_PORT}")
        print(f"   数据库名: {config.MYSQL_DATABASE}")
        print(f"   Redis主机: {config.REDIS_HOST}:{config.REDIS_PORT}")
        
        # 检查安全配置
        print(f"\n🛡️  安全配置:")
        print(f"   管理员用户名: {config.ADMIN_USERNAME}")
        print(f"   管理员密码: {'已设置' if config.ADMIN_PASSWORD != 'admin123' else '⚠️  使用默认值'}")
        print(f"   会话超时: {config.ADMIN_SESSION_TIMEOUT}秒")
        print(f"   需要鉴权: {config.REQUIRE_ADMIN_AUTH}")
        
        # 生产环境检查
        if config.FLASK_ENV == 'production':
            print(f"\n🚀 生产环境检查:")
            issues = []
            
            if config.SECRET_KEY == 'dev-secret-key-change-this':
                issues.append("SECRET_KEY使用默认值")
            
            if config.ADMIN_PASSWORD == 'admin123':
                issues.append("ADMIN_PASSWORD使用默认值")
            
            if not config.BASE_URL.startswith('https://'):
                issues.append("BASE_URL不是HTTPS")
            
            if not use_https and not behind_proxy:
                issues.append("未启用HTTPS配置")
            
            if issues:
                print("   ⚠️  发现问题:")
                for issue in issues:
                    print(f"      - {issue}")
            else:
                print("   ✅ 生产环境配置正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_flask_app():
    """测试Flask应用配置"""
    print(f"\n🌐 Flask应用配置测试:")
    
    try:
        from app import app
        
        # 检查Flask配置
        print(f"   SESSION_COOKIE_SECURE: {app.config.get('SESSION_COOKIE_SECURE', False)}")
        print(f"   SESSION_COOKIE_HTTPONLY: {app.config.get('SESSION_COOKIE_HTTPONLY', False)}")
        print(f"   SESSION_COOKIE_SAMESITE: {app.config.get('SESSION_COOKIE_SAMESITE', 'None')}")
        print(f"   PREFERRED_URL_SCHEME: {app.config.get('PREFERRED_URL_SCHEME', 'http')}")
        
        # 检查ProxyFix中间件
        if hasattr(app, 'wsgi_app') and hasattr(app.wsgi_app, '__class__'):
            if 'ProxyFix' in str(app.wsgi_app.__class__):
                print(f"   ✅ ProxyFix中间件已启用")
            else:
                print(f"   ⚠️  ProxyFix中间件未检测到")
        
        print(f"   ✅ Flask应用配置加载成功")
        return True
        
    except Exception as e:
        print(f"   ❌ Flask应用配置测试失败: {e}")
        return False

def test_dependencies():
    """测试依赖包"""
    print(f"\n📦 依赖包测试:")
    
    required_packages = [
        'flask',
        'werkzeug',
        'redis',
        'mysql.connector',
        'requests',
        'bs4',
        'apscheduler',
        'dotenv',
        'pytz'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺失的依赖包: {', '.join(missing_packages)}")
        print(f"   请运行: pip install -r requirements.txt")
        return False
    
    return True

def main():
    """主函数"""
    print("开始配置测试...\n")
    
    # 检查环境变量文件
    if os.path.exists('.env'):
        print("✅ 找到 .env 配置文件")
    else:
        print("⚠️  未找到 .env 配置文件")
        print("   建议复制 .env.production.example 为 .env 并修改配置")
    
    # 运行测试
    tests = [
        ("配置测试", test_config),
        ("依赖包测试", test_dependencies),
        ("Flask应用测试", test_flask_app)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*50}")
    print(f"测试总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n通过率: {passed}/{len(results)} ({passed/len(results)*100:.1f}%)")
    
    if passed == len(results):
        print(f"\n🎉 所有测试通过！应用已准备好部署。")
        print(f"\n下一步:")
        print(f"   1. 配置nginx反向代理")
        print(f"   2. 设置SSL证书")
        print(f"   3. 启动应用: python app.py")
    else:
        print(f"\n⚠️  请解决上述问题后重新测试。")
    
    return passed == len(results)

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
