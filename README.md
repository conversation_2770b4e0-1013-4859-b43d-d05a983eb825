# ID共享租用系统

基于Flask的ID账号共享租用系统，支持分享链接24小时时效控制、账号池永久存储和批量链接生成功能。

## 系统特性

- **分享链接控制**：每个链接有24小时有效期，首次访问激活
- **账号池管理**：定期从源页面获取账号信息，永久存储到Redis
- **双重验证**：MySQL存储UUID鉴权，Redis控制时效性
- **批量生成**：支持脚本批量生成分享链接
- **响应式界面**：现代化Web管理界面

## 环境配置

### 1. 创建环境变量文件

复制示例配置文件：
```bash
cp env_example .env
```

### 2. 配置环境变量

编辑 `.env` 文件，修改相应配置：

```bash
# Flask 应用配置
FLASK_ENV=development
SECRET_KEY=your-secret-key-change-this-in-production
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
DEBUG=True

# MySQL 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your_username
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=idshare_db
MYSQL_CHARSET=utf8mb4

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# 系统配置
BASE_URL=http://localhost:5000
UPDATE_INTERVAL=5
SHARE_EXPIRE_HOURS=24

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=app.log

# 安全配置
MAX_LINKS_PER_BATCH=10000
RATE_LIMIT_ENABLED=False
```

### 3. 生产环境配置

生产环境需要设置以下必需的环境变量：
- `SECRET_KEY`：Flask密钥（必须修改）
- `MYSQL_HOST`、`MYSQL_USER`、`MYSQL_PASSWORD`、`MYSQL_DATABASE`：数据库配置
- `BASE_URL`：实际域名地址

```bash
export FLASK_ENV=production
export SECRET_KEY=your-production-secret-key
export MYSQL_HOST=your-mysql-host
export MYSQL_USER=your-mysql-user
export MYSQL_PASSWORD=your-mysql-password
export BASE_URL=https://yourdomain.com
```

## 系统安装

### 1. 环境依赖

- Python 3.8+
- MySQL 5.7+
- Redis 6.0+

### 2. 安装Python依赖

```bash
pip install -r requirements.txt
```

### 3. 数据库初始化

确保MySQL和Redis服务运行，首次启动应用会自动创建数据库表：

```bash
python app.py
```

## 功能模块

### 1. 账号池管理

- **自动更新**：每5分钟（可配置）从账号源获取最新信息
- **永久存储**：账号信息存储在Redis中，无过期时间
- **智能提取**：支持多种HTML结构的账号信息提取

### 2. 分享链接系统

- **UUID生成**：存储在MySQL中用于鉴权
- **时效控制**：Redis控制24小时访问期限
- **激活机制**：首次访问激活，重复访问检查有效期

### 3. 批量链接生成

#### 简化版脚本（推荐日常使用）
```bash
# 查看配置
python batch_generate.py --config

# 生成100个链接
python batch_generate.py 100

# 生成到指定文件
python batch_generate.py 50 vip_links
```

#### 完整功能脚本
```bash
# 开发环境生成
python generate_links.py --count 100

# 生产环境生成
python generate_links.py --count 100 --env production

# 自定义参数
python generate_links.py \
  --count 100 \
  --base-url https://yourdomain.com \
  --output premium_links.txt \
  --env production

# 验证链接
python generate_links.py --verify

# 查看配置
python generate_links.py --config
```

## Web界面

### 首页
- 系统状态监控
- 账号池统计信息
- 实时数据刷新

### 管理页面 (`/admin`)
- 账号源管理
- 手动更新触发
- 系统统计信息
- 链接生成说明

## API接口

### 账号信息
```http
GET /api/accounts
```

### 系统统计
```http
GET /api/stats
```

### 手动更新
```http
POST /api/trigger_update
```

### 添加账号源
```http
POST /api/add_share_url
Content-Type: application/json

{
  "share_url": "https://example.com/share"
}
```

## 数据存储架构

### MySQL表结构

#### share_access（分享访问控制）
```sql
CREATE TABLE share_access (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL,
    is_activated BOOLEAN DEFAULT FALSE,
    activated_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### share_config（分享链接配置）
```sql
CREATE TABLE share_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    share_url VARCHAR(500) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Redis数据结构

#### 账号池（永久存储）
```
Key: accounts_pool
Value: {
  "accounts": [...],
  "updated_at": "2024-03-15T14:30:22",
  "count": 100
}
```

#### 分享链接控制（24小时过期）
```
Key: share:uuid
Value: {
  "activated_at": "2024-03-15T14:30:22",
  "status": "active"
}
TTL: 86400秒
```

## 部署指南

### 1. 开发环境
```bash
# 设置环境变量
cp env_example .env
# 编辑 .env 文件

# 启动应用
python app.py
```

### 2. 生产环境
```bash
# 设置生产环境变量
export FLASK_ENV=production
export SECRET_KEY=your-production-secret-key
# ... 其他必需变量

# 使用WSGI服务器
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### 3. Docker部署
```dockerfile
FROM python:3.9

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

ENV FLASK_ENV=production
EXPOSE 5000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

## 监控和维护

### 1. 日志监控
系统日志输出到文件和控制台：
```bash
tail -f app.log
```

### 2. 系统健康检查
```bash
# 检查Redis连接
redis-cli ping

# 检查MySQL连接
mysql -h localhost -u username -p

# 验证生成的链接
python generate_links.py --verify
```

### 3. 定期维护
- 清理过期的分享记录
- 监控账号池更新状态
- 备份重要数据

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `.env` 文件中的数据库配置
   - 确认MySQL服务是否运行

2. **Redis连接失败**
   - 检查Redis服务状态
   - 验证Redis配置参数

3. **账号提取失败**
   - 检查账号源URL是否可访问
   - 验证HTML结构是否发生变化

4. **分享链接无效**
   - 检查UUID是否存在于数据库
   - 验证Redis中的时效性控制

### 配置验证
```bash
# 查看当前配置
python generate_links.py --config
python batch_generate.py --config
```

## 安全注意事项

1. **生产环境**：必须修改 `SECRET_KEY`
2. **数据库安全**：使用强密码，限制访问权限
3. **Redis安全**：配置密码认证
4. **网络安全**：使用HTTPS，配置防火墙
5. **访问控制**：实施适当的访问限制

## 许可证

MIT License 