@echo off
echo =====================================
echo 数据库快速修复脚本
echo =====================================
echo.

echo 1. 测试MySQL连接...
mysql -u idshare -pbifT5esQP5ATXHT7 -e "SELECT 'MySQL连接成功' AS status;"
if %errorlevel% neq 0 (
    echo MySQL连接失败，请检查配置
    pause
    exit /b 1
)

echo.
echo 2. 执行数据库修复...
mysql -u idshare -pbifT5esQP5ATXHT7 < fix_database.sql
if %errorlevel% neq 0 (
    echo 数据库修复失败
    pause
    exit /b 1
)

echo.
echo 3. 验证表结构...
mysql -u idshare -pbifT5esQP5ATXHT7 idshare -e "SHOW TABLE STATUS;"

echo.
echo 4. 测试Redis连接...
redis-cli -p 6379 -n 8 ping
if %errorlevel% neq 0 (
    echo Redis连接可能有问题，请检查配置
)

echo.
echo =====================================
echo 修复完成！现在可以测试应用了
echo =====================================
echo.
echo 运行以下命令测试：
echo python generate_links.py --count 5
echo python app.py
echo.
pause 